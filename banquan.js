// ==UserScript==
// @name         版权注册自动填表助手
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  使用AI自动填写版权注册表单
// <AUTHOR>
// @match        https://register.ccopyright.com.cn/r11.html*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_xmlhttpRequest
// ==/UserScript==

(function() {
    'use strict';

    // AI配置界面
    function createConfigPanel() {
        const panel = document.createElement('div');
        panel.id = 'ai-config-panel';
        panel.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 350px;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 15px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-family: Arial, sans-serif;
        `;

        panel.innerHTML = `
            <div style="margin-bottom: 15px; font-weight: bold; color: #007bff; font-size: 16px;">
                🤖 AI自动填表配置
            </div>

            <div style="margin-bottom: 10px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">API地址:</label>
                <input type="text" id="api-url" placeholder="https://api.openai.com/v1/chat/completions"
                       style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>

            <div style="margin-bottom: 10px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">API密钥:</label>
                <input type="password" id="api-key" placeholder="sk-..."
                       style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">模型名称:</label>
                <input type="text" id="model-name" placeholder="gpt-3.5-turbo"
                       style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>

            <div style="margin-bottom: 10px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">软件名称:</label>
                <input type="text" id="software-name" placeholder="请输入软件名称"
                       style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">开发框架:</label>
                <input type="text" id="dev-framework" placeholder="如: Vue.js, React, Spring Boot等"
                       style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>

            <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                <button id="save-config" style="flex: 1; padding: 10px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    保存配置
                </button>
                <button id="start-auto-fill" style="flex: 1; padding: 10px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    开始填表
                </button>
            </div>

            <button id="toggle-panel" style="width: 100%; padding: 8px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
                最小化
            </button>

            <div id="status" style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px; font-size: 12px; display: none;"></div>
        `;

        document.body.appendChild(panel);

        // 加载保存的配置
        loadConfig();

        // 绑定事件
        document.getElementById('save-config').onclick = saveConfig;
        document.getElementById('start-auto-fill').onclick = startAutoFill;
        document.getElementById('toggle-panel').onclick = togglePanel;
    }

    // 加载配置
    function loadConfig() {
        document.getElementById('api-url').value = GM_getValue('api-url', '');
        document.getElementById('api-key').value = GM_getValue('api-key', '');
        document.getElementById('model-name').value = GM_getValue('model-name', 'gpt-3.5-turbo');
        document.getElementById('software-name').value = GM_getValue('software-name', '');
        document.getElementById('dev-framework').value = GM_getValue('dev-framework', '');
    }

    // 保存配置
    function saveConfig() {
        GM_setValue('api-url', document.getElementById('api-url').value);
        GM_setValue('api-key', document.getElementById('api-key').value);
        GM_setValue('model-name', document.getElementById('model-name').value);
        GM_setValue('software-name', document.getElementById('software-name').value);
        GM_setValue('dev-framework', document.getElementById('dev-framework').value);

        showStatus('配置已保存！', 'success');
    }

    // 切换面板显示
    function togglePanel() {
        const panel = document.getElementById('ai-config-panel');
        const isMinimized = panel.style.height === '50px';

        if (isMinimized) {
            panel.style.height = 'auto';
            document.getElementById('toggle-panel').textContent = '最小化';
        } else {
            panel.style.height = '50px';
            panel.style.overflow = 'hidden';
            document.getElementById('toggle-panel').textContent = '展开';
        }
    }

    // 显示状态信息
    function showStatus(message, type = 'info') {
        const status = document.getElementById('status');
        status.style.display = 'block';
        status.textContent = message;
        status.style.background = type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1';
        status.style.color = type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460';

        setTimeout(() => {
            status.style.display = 'none';
        }, 3000);
    }

    // AI提示词模板
    function generatePrompt(softwareName, framework) {
        return `你是一个专业的软件版权注册助手。请根据以下信息生成软件版权注册表单的各项内容：

软件名称：${softwareName}
开发框架：${framework}

请为以下字段生成合适的内容，要求专业、准确、符合版权注册规范：

1. 开发的硬件环境（50字以内）
2. 运行的硬件环境（50字以内）
3. 开发该软件的操作系统（100字以内）
4. 软件开发环境/开发工具（50字以内）
5. 该软件的运行平台/操作系统（50字以内）
6. 软件运行支撑环境/支持软件（100字以内）
7. 开发目的（100字以内）
8. 面向领域/行业（30字以内）
9. 软件的主要功能（150字以内）
10. 软件的技术特点（100字以内）

请以JSON格式返回，格式如下：
{
  "devHardware": "开发硬件环境内容",
  "runHardware": "运行硬件环境内容",
  "devOS": "开发操作系统内容",
  "devTools": "开发工具内容",
  "runPlatform": "运行平台内容",
  "supportSoftware": "支撑环境内容",
  "purpose": "开发目的内容",
  "industry": "面向领域内容",
  "mainFunction": "主要功能内容",
  "techFeatures": "技术特点内容"
}`;
    }

    // 调用AI接口
    async function callAI(prompt) {
        const apiUrl = GM_getValue('api-url');
        const apiKey = GM_getValue('api-key');
        const modelName = GM_getValue('model-name');

        if (!apiUrl || !apiKey) {
            throw new Error('请先配置API地址和密钥');
        }

        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: apiUrl,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                data: JSON.stringify({
                    model: modelName,
                    messages: [
                        {
                            role: 'user',
                            content: prompt
                        }
                    ],
                    temperature: 0.7
                }),
                onload: function(response) {
                    try {
                        const data = JSON.parse(response.responseText);
                        if (data.choices && data.choices[0]) {
                            const content = data.choices[0].message.content;
                            // 尝试解析JSON
                            const jsonMatch = content.match(/\{[\s\S]*\}/);
                            if (jsonMatch) {
                                const result = JSON.parse(jsonMatch[0]);
                                resolve(result);
                            } else {
                                reject(new Error('AI返回格式错误'));
                            }
                        } else {
                            reject(new Error('AI响应格式错误'));
                        }
                    } catch (error) {
                        reject(new Error('解析AI响应失败: ' + error.message));
                    }
                },
                onerror: function(error) {
                    reject(new Error('API调用失败: ' + error.message));
                }
            });
        });
    }

    // 等待元素出现
    function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            function check() {
                const element = document.querySelector(selector) ||
                               document.evaluate(selector, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;

                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`元素未找到: ${selector}`));
                } else {
                    setTimeout(check, 100);
                }
            }

            check();
        });
    }

    // 填写表单字段
    async function fillField(selector, value, isTextarea = false) {
        try {
            const element = await waitForElement(selector);

            if (isTextarea) {
                element.value = value;
                element.dispatchEvent(new Event('input', { bubbles: true }));
                element.dispatchEvent(new Event('change', { bubbles: true }));
            } else {
                element.value = value;
                element.dispatchEvent(new Event('input', { bubbles: true }));
                element.dispatchEvent(new Event('change', { bubbles: true }));
            }

            await new Promise(resolve => setTimeout(resolve, 500));
            return true;
        } catch (error) {
            console.error(`填写字段失败 ${selector}:`, error);
            return false;
        }
    }

    // 点击按钮
    async function clickButton(selector) {
        try {
            const element = await waitForElement(selector);
            element.click();
            await new Promise(resolve => setTimeout(resolve, 1000));
            return true;
        } catch (error) {
            console.error(`点击按钮失败 ${selector}:`, error);
            return false;
        }
    }

    // 开始自动填表
    async function startAutoFill() {
        try {
            showStatus('开始自动填表...', 'info');

            const softwareName = document.getElementById('software-name').value;
            const framework = document.getElementById('dev-framework').value;

            if (!softwareName || !framework) {
                showStatus('请先填写软件名称和开发框架', 'error');
                return;
            }

            // 验证表单
            if (!validateForm()) {
                return;
            }

            // 使用增强版填表函数
            await enhancedAutoFill();

        } catch (error) {
            console.error('自动填表失败:', error);
            showStatus('填表失败: ' + error.message, 'error');
            if (typeof handleError === 'function') {
                handleError(error, '自动填表');
            }
        }
    }

    // 创建快捷操作按钮
    function createQuickActions() {
        const quickPanel = document.createElement('div');
        quickPanel.id = 'quick-actions';
        quickPanel.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 9999;
        `;

        quickPanel.innerHTML = `
            <button id="quick-next" style="padding: 10px 15px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                快速下一步
            </button>
            <button id="quick-select-app" style="padding: 10px 15px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                选择应用软件
            </button>
            <button id="quick-select-today" style="padding: 10px 15px; background: #ffc107; color: black; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                选择今天
            </button>
        `;

        document.body.appendChild(quickPanel);

        // 绑定快捷操作事件
        document.getElementById('quick-next').onclick = () => {
            clickButton("//button[contains(text(),'下一步')]");
        };

        document.getElementById('quick-select-app').onclick = () => {
            clickButton("//div[contains(text(),'应用软件')]");
        };

        document.getElementById('quick-select-today').onclick = () => {
            clickButton("//span[contains(text(),'今天')]");
        };
    }

    // 创建预设模板功能
    function createTemplateManager() {
        const templates = {
            'web应用': {
                framework: 'Vue.js + Spring Boot',
                devHardware: 'Intel i7处理器，16GB内存，SSD硬盘的开发工作站',
                runHardware: '支持x86_64架构的服务器，最低2GB内存',
                devOS: 'Windows 10/11 或 macOS 或 Ubuntu 18.04+',
                devTools: 'Visual Studio Code, IntelliJ IDEA, Node.js, Maven',
                runPlatform: 'Linux/Windows Server, 支持Java 8+环境',
                supportSoftware: 'MySQL 5.7+, Redis 5.0+, Nginx 1.18+',
                purpose: '为用户提供便捷的在线服务，提升业务效率和用户体验',
                industry: '互联网服务',
                mainFunction: '用户注册登录、数据管理、业务流程处理、报表统计等核心功能',
                techFeatures: '采用前后端分离架构，响应式设计，RESTful API，支持高并发访问'
            },
            '移动应用': {
                framework: 'React Native',
                devHardware: 'MacBook Pro M1，16GB内存，配备iOS和Android开发环境',
                runHardware: 'iOS 12+设备或Android 8.0+设备，最低2GB运行内存',
                devOS: 'macOS 12+ 或 Windows 10+',
                devTools: 'Xcode, Android Studio, React Native CLI, Metro',
                runPlatform: 'iOS 12+ / Android 8.0+',
                supportSoftware: 'Node.js 16+, React Native 0.68+, 原生模块支持',
                purpose: '为移动端用户提供便捷的移动应用服务',
                industry: '移动互联网',
                mainFunction: '移动端界面交互、数据同步、推送通知、离线缓存等功能',
                techFeatures: '跨平台开发，原生性能，热更新支持，响应式布局'
            },
            '桌面应用': {
                framework: 'Electron + Vue.js',
                devHardware: 'Intel/AMD多核处理器，8GB以上内存，SSD存储',
                runHardware: 'Windows/macOS/Linux桌面环境，最低4GB内存',
                devOS: 'Windows 10, macOS 10.14+, Ubuntu 18.04+',
                devTools: 'Visual Studio Code, Node.js, Electron Builder',
                runPlatform: 'Windows 10+, macOS 10.14+, Linux发行版',
                supportSoftware: 'Node.js 14+, Chromium内核, 系统原生API',
                purpose: '为用户提供功能丰富的桌面应用解决方案',
                industry: '软件工具',
                mainFunction: '文件处理、数据管理、系统集成、用户界面交互等',
                techFeatures: '跨平台兼容，原生系统集成，自动更新，丰富的UI组件'
            }
        };

        const templatePanel = document.createElement('div');
        templatePanel.id = 'template-panel';
        templatePanel.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            width: 300px;
            background: white;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-family: Arial, sans-serif;
        `;

        templatePanel.innerHTML = `
            <div style="margin-bottom: 15px; font-weight: bold; color: #28a745; font-size: 16px;">
                📋 快速模板
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">选择模板:</label>
                <select id="template-select" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    <option value="">请选择模板</option>
                    <option value="web应用">Web应用模板</option>
                    <option value="移动应用">移动应用模板</option>
                    <option value="桌面应用">桌面应用模板</option>
                </select>
            </div>

            <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                <button id="apply-template" style="flex: 1; padding: 10px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    应用模板
                </button>
                <button id="toggle-template" style="flex: 1; padding: 8px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    最小化
                </button>
            </div>
        `;

        document.body.appendChild(templatePanel);

        // 应用模板
        document.getElementById('apply-template').onclick = () => {
            const selectedTemplate = document.getElementById('template-select').value;
            if (selectedTemplate && templates[selectedTemplate]) {
                const template = templates[selectedTemplate];

                // 填充到配置面板
                document.getElementById('dev-framework').value = template.framework;

                showStatus(`已应用${selectedTemplate}模板`, 'success');
            } else {
                showStatus('请先选择一个模板', 'error');
            }
        };

        // 最小化模板面板
        document.getElementById('toggle-template').onclick = () => {
            const panel = document.getElementById('template-panel');
            const isMinimized = panel.style.height === '50px';

            if (isMinimized) {
                panel.style.height = 'auto';
                document.getElementById('toggle-template').textContent = '最小化';
            } else {
                panel.style.height = '50px';
                panel.style.overflow = 'hidden';
                document.getElementById('toggle-template').textContent = '展开';
            }
        };
    }

    // 创建进度指示器
    function createProgressIndicator() {
        const progressPanel = document.createElement('div');
        progressPanel.id = 'progress-panel';
        progressPanel.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 20px;
            z-index: 10001;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-family: Arial, sans-serif;
            display: none;
        `;

        progressPanel.innerHTML = `
            <div style="margin-bottom: 15px; font-weight: bold; color: #007bff; font-size: 18px; text-align: center;">
                🚀 自动填表进度
            </div>

            <div id="progress-bar" style="width: 100%; height: 20px; background: #f0f0f0; border-radius: 10px; margin-bottom: 15px; overflow: hidden;">
                <div id="progress-fill" style="height: 100%; background: linear-gradient(90deg, #007bff, #28a745); width: 0%; transition: width 0.3s ease;"></div>
            </div>

            <div id="progress-text" style="text-align: center; margin-bottom: 15px; font-size: 14px;">
                准备开始...
            </div>

            <div id="progress-steps" style="font-size: 12px; color: #666;">
                <div>✓ 配置检查</div>
                <div>⏳ 填写基本信息</div>
                <div>⏳ 选择软件类型</div>
                <div>⏳ AI内容生成</div>
                <div>⏳ 填写详细信息</div>
            </div>

            <button id="close-progress" style="width: 100%; padding: 10px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 15px;">
                关闭
            </button>
        `;

        document.body.appendChild(progressPanel);

        document.getElementById('close-progress').onclick = () => {
            progressPanel.style.display = 'none';
        };
    }

    // 更新进度
    function updateProgress(step, total, message) {
        const progressPanel = document.getElementById('progress-panel');
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');

        if (progressPanel) {
            progressPanel.style.display = 'block';
            const percentage = (step / total) * 100;
            progressFill.style.width = percentage + '%';
            progressText.textContent = message;
        }
    }

    // 增强的自动填表函数
    async function enhancedAutoFill() {
        try {
            updateProgress(0, 10, '开始自动填表...');
            debugLog('开始增强版自动填表');

            const softwareName = document.getElementById('software-name').value;
            const framework = document.getElementById('dev-framework').value;

            if (!softwareName || !framework) {
                showStatus('请先填写软件名称和开发框架', 'error');
                return;
            }

            updateProgress(1, 10, '填写软件名称...');
            await smartFillField("//input[@placeholder='请输入软件全称']", softwareName);

            updateProgress(2, 10, '填写版本号...');
            await smartFillField("//input[@placeholder='请输入版本号']", "v1.0");

            updateProgress(3, 10, '点击下一步...');
            await clickButton("//button[contains(text(),'下一步')]");

            updateProgress(4, 10, '选择应用软件...');
            await clickButton("//div[contains(text(),'应用软件')]");

            updateProgress(5, 10, '选择今天...');
            await clickButton("//span[contains(text(),'今天')]");

            updateProgress(6, 10, '进入详细信息页面...');
            await clickButton("//button[contains(text(),'下一步')]");

            updateProgress(7, 10, '正在调用AI生成内容...');
            const prompt = generatePrompt(softwareName, framework);
            const aiResult = await enhancedCallAI(prompt);

            updateProgress(8, 10, '填写AI生成的详细信息...');

            // 批量填写所有字段
            const fields = [
                { selector: "(//textarea[@placeholder='请输入...'])[1]", value: aiResult.devHardware },
                { selector: "(//textarea[@placeholder='请输入...'])[2]", value: aiResult.runHardware },
                { selector: "(//textarea[@class='large blur'])[1]", value: aiResult.devOS },
                { selector: "(//textarea[@placeholder='请输入...'])[4]", value: aiResult.devTools },
                { selector: "(//textarea[@placeholder='请输入...'])[5]", value: aiResult.runPlatform },
                { selector: "(//textarea[@class='large blur'])[2]", value: aiResult.supportSoftware },
                { selector: "(//textarea[@class='large blur'])[3]", value: aiResult.purpose },
                { selector: "(//textarea[@placeholder='请输入...'])[8]", value: aiResult.industry },
                { selector: "(//textarea[@placeholder='请输入...'])[9]", value: aiResult.mainFunction },
                { selector: "(//textarea[@class='large blur'])[4]", value: aiResult.techFeatures }
            ];

            for (let i = 0; i < fields.length; i++) {
                const field = fields[i];
                updateProgress(8 + (i / fields.length), 10, `填写字段 ${i + 1}/${fields.length}...`);
                await smartFillField(field.selector, field.value, { isTextarea: true });
            }

            updateProgress(10, 10, '自动填表完成！');
            showStatus('所有字段填写完成！', 'success');
            debugLog('增强版自动填表完成');

            // 3秒后自动关闭进度面板
            setTimeout(() => {
                const progressPanel = document.getElementById('progress-panel');
                if (progressPanel) {
                    progressPanel.style.display = 'none';
                }
            }, 3000);

        } catch (error) {
            console.error('自动填表失败:', error);
            showStatus('填表失败: ' + error.message, 'error');
            updateProgress(0, 10, '填表失败: ' + error.message);
            debugLog(`自动填表失败: ${error.message}`);
            if (typeof handleError === 'function') {
                handleError(error, '增强版自动填表');
            }
        }
    }

    // 创建调试面板
    function createDebugPanel() {
        const debugPanel = document.createElement('div');
        debugPanel.id = 'debug-panel';
        debugPanel.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 350px;
            background: #2d3748;
            color: white;
            border-radius: 8px;
            padding: 15px;
            z-index: 10000;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            display: none;
        `;

        debugPanel.innerHTML = `
            <div style="margin-bottom: 10px; font-weight: bold; color: #4fd1c7; font-size: 14px;">
                🔧 调试信息
            </div>
            <div id="debug-content" style="white-space: pre-wrap; line-height: 1.4;"></div>
            <div style="margin-top: 10px; display: flex; gap: 10px;">
                <button id="clear-debug" style="padding: 5px 10px; background: #e53e3e; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 11px;">
                    清空
                </button>
                <button id="toggle-debug" style="padding: 5px 10px; background: #4a5568; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 11px;">
                    隐藏
                </button>
            </div>
        `;

        document.body.appendChild(debugPanel);

        // 绑定调试面板事件
        document.getElementById('clear-debug').onclick = () => {
            document.getElementById('debug-content').textContent = '';
        };

        document.getElementById('toggle-debug').onclick = () => {
            debugPanel.style.display = 'none';
        };
    }

    // 调试日志函数
    function debugLog(message) {
        const debugContent = document.getElementById('debug-content');
        if (debugContent) {
            const timestamp = new Date().toLocaleTimeString();
            debugContent.textContent += `[${timestamp}] ${message}\n`;
            debugContent.scrollTop = debugContent.scrollHeight;
        }
        console.log(`[版权助手] ${message}`);
    }

    // 创建设置面板
    function createSettingsPanel() {
        const settingsPanel = document.createElement('div');
        settingsPanel.id = 'settings-panel';
        settingsPanel.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 500px;
            background: white;
            border: 2px solid #6f42c1;
            border-radius: 8px;
            padding: 20px;
            z-index: 10002;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-family: Arial, sans-serif;
            display: none;
        `;

        settingsPanel.innerHTML = `
            <div style="margin-bottom: 20px; font-weight: bold; color: #6f42c1; font-size: 18px; text-align: center;">
                ⚙️ 高级设置
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">填写延迟(ms):</label>
                    <input type="number" id="fill-delay" value="500" min="100" max="5000"
                           style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>

                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">点击延迟(ms):</label>
                    <input type="number" id="click-delay" value="1000" min="500" max="5000"
                           style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>

                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">AI温度:</label>
                    <input type="number" id="ai-temperature" value="0.7" min="0" max="2" step="0.1"
                           style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>

                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">最大重试次数:</label>
                    <input type="number" id="max-retries" value="3" min="1" max="10"
                           style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                    <input type="checkbox" id="auto-scroll" checked> 自动滚动到填写位置
                </label>
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                    <input type="checkbox" id="debug-mode"> 启用调试模式
                </label>
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                    <input type="checkbox" id="backup-data" checked> 自动备份填写数据
                </label>
            </div>

            <div style="display: flex; gap: 10px;">
                <button id="save-settings" style="flex: 1; padding: 10px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    保存设置
                </button>
                <button id="reset-settings" style="flex: 1; padding: 10px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    重置设置
                </button>
                <button id="close-settings" style="flex: 1; padding: 10px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    关闭
                </button>
            </div>
        `;

        document.body.appendChild(settingsPanel);

        // 加载设置
        loadSettings();

        // 绑定设置面板事件
        document.getElementById('save-settings').onclick = saveSettings;
        document.getElementById('reset-settings').onclick = resetSettings;
        document.getElementById('close-settings').onclick = () => {
            settingsPanel.style.display = 'none';
        };
    }

    // 加载设置
    function loadSettings() {
        document.getElementById('fill-delay').value = GM_getValue('fill-delay', 500);
        document.getElementById('click-delay').value = GM_getValue('click-delay', 1000);
        document.getElementById('ai-temperature').value = GM_getValue('ai-temperature', 0.7);
        document.getElementById('max-retries').value = GM_getValue('max-retries', 3);
        document.getElementById('auto-scroll').checked = GM_getValue('auto-scroll', true);
        document.getElementById('debug-mode').checked = GM_getValue('debug-mode', false);
        document.getElementById('backup-data').checked = GM_getValue('backup-data', true);
    }

    // 保存设置
    function saveSettings() {
        GM_setValue('fill-delay', parseInt(document.getElementById('fill-delay').value));
        GM_setValue('click-delay', parseInt(document.getElementById('click-delay').value));
        GM_setValue('ai-temperature', parseFloat(document.getElementById('ai-temperature').value));
        GM_setValue('max-retries', parseInt(document.getElementById('max-retries').value));
        GM_setValue('auto-scroll', document.getElementById('auto-scroll').checked);
        GM_setValue('debug-mode', document.getElementById('debug-mode').checked);
        GM_setValue('backup-data', document.getElementById('backup-data').checked);

        showStatus('设置已保存！', 'success');

        // 如果启用了调试模式，显示调试面板
        if (GM_getValue('debug-mode', false)) {
            document.getElementById('debug-panel').style.display = 'block';
        } else {
            document.getElementById('debug-panel').style.display = 'none';
        }
    }

    // 重置设置
    function resetSettings() {
        if (confirm('确定要重置所有设置吗？')) {
            GM_setValue('fill-delay', 500);
            GM_setValue('click-delay', 1000);
            GM_setValue('ai-temperature', 0.7);
            GM_setValue('max-retries', 3);
            GM_setValue('auto-scroll', true);
            GM_setValue('debug-mode', false);
            GM_setValue('backup-data', true);

            loadSettings();
            showStatus('设置已重置！', 'success');
        }
    }

    // 创建主菜单
    function createMainMenu() {
        const menuButton = document.createElement('div');
        menuButton.id = 'main-menu-button';
        menuButton.style.cssText = `
            position: fixed;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50% 0 0 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10003;
            box-shadow: -2px 0 10px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        `;

        menuButton.innerHTML = `
            <span style="color: white; font-size: 20px; margin-left: -5px;">🤖</span>
        `;

        const menu = document.createElement('div');
        menu.id = 'main-menu';
        menu.style.cssText = `
            position: fixed;
            top: 50%;
            right: -300px;
            transform: translateY(-50%);
            width: 280px;
            background: white;
            border: 2px solid #667eea;
            border-radius: 10px 0 0 10px;
            padding: 20px;
            z-index: 10003;
            box-shadow: -5px 0 15px rgba(0,0,0,0.2);
            transition: right 0.3s ease;
            font-family: Arial, sans-serif;
        `;

        menu.innerHTML = `
            <div style="margin-bottom: 20px; font-weight: bold; color: #667eea; font-size: 16px; text-align: center;">
                🚀 版权注册助手
            </div>

            <div style="display: flex; flex-direction: column; gap: 10px;">
                <button id="menu-config" style="padding: 12px; background: #007bff; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">
                    📝 配置管理
                </button>
                <button id="menu-template" style="padding: 12px; background: #28a745; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">
                    📋 模板管理
                </button>
                <button id="menu-settings" style="padding: 12px; background: #6f42c1; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">
                    ⚙️ 高级设置
                </button>
                <button id="menu-debug" style="padding: 12px; background: #17a2b8; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">
                    🔧 调试面板
                </button>
                <button id="menu-help" style="padding: 12px; background: #ffc107; color: black; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">
                    ❓ 使用帮助
                </button>
            </div>
        `;

        document.body.appendChild(menuButton);
        document.body.appendChild(menu);

        let menuOpen = false;

        // 菜单切换
        menuButton.onclick = () => {
            menuOpen = !menuOpen;
            if (menuOpen) {
                menu.style.right = '0px';
                menuButton.style.right = '280px';
            } else {
                menu.style.right = '-300px';
                menuButton.style.right = '0px';
            }
        };

        // 菜单项事件绑定
        document.getElementById('menu-config').onclick = () => {
            const configPanel = document.getElementById('ai-config-panel');
            configPanel.style.display = configPanel.style.display === 'none' ? 'block' : 'block';
        };

        document.getElementById('menu-template').onclick = () => {
            const templatePanel = document.getElementById('template-panel');
            templatePanel.style.display = templatePanel.style.display === 'none' ? 'block' : 'block';
        };

        document.getElementById('menu-settings').onclick = () => {
            document.getElementById('settings-panel').style.display = 'block';
        };

        document.getElementById('menu-debug').onclick = () => {
            const debugPanel = document.getElementById('debug-panel');
            debugPanel.style.display = debugPanel.style.display === 'none' ? 'block' : 'none';
        };

        document.getElementById('menu-help').onclick = () => {
            showHelpDialog();
        };
    }

    // 显示帮助对话框
    function showHelpDialog() {
        const helpDialog = document.createElement('div');
        helpDialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 10004;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        helpDialog.innerHTML = `
            <div style="background: white; border-radius: 10px; padding: 30px; max-width: 600px; max-height: 80vh; overflow-y: auto;">
                <h2 style="color: #007bff; margin-bottom: 20px; text-align: center;">📖 使用帮助</h2>

                <div style="line-height: 1.6; color: #333;">
                    <h3 style="color: #28a745;">🚀 快速开始</h3>
                    <ol>
                        <li>点击右侧的机器人图标打开主菜单</li>
                        <li>在"配置管理"中设置AI接口信息</li>
                        <li>填写软件名称和开发框架</li>
                        <li>点击"开始填表"自动完成表单填写</li>
                    </ol>

                    <h3 style="color: #28a745;">⚙️ 配置说明</h3>
                    <ul>
                        <li><strong>API地址:</strong> 支持OpenAI兼容的API接口</li>
                        <li><strong>API密钥:</strong> 您的API密钥，会安全保存在本地</li>
                        <li><strong>模型名称:</strong> 如gpt-3.5-turbo、gpt-4等</li>
                        <li><strong>软件名称:</strong> 要注册版权的软件名称</li>
                        <li><strong>开发框架:</strong> 软件使用的技术栈</li>
                    </ul>

                    <h3 style="color: #28a745;">📋 模板功能</h3>
                    <p>提供了Web应用、移动应用、桌面应用三种预设模板，可快速填充常用配置。</p>

                    <h3 style="color: #28a745;">🔧 高级功能</h3>
                    <ul>
                        <li><strong>调试模式:</strong> 显示详细的执行日志</li>
                        <li><strong>自动滚动:</strong> 自动滚动到当前填写位置</li>
                        <li><strong>数据备份:</strong> 自动备份填写的数据</li>
                        <li><strong>延迟设置:</strong> 调整填写和点击的延迟时间</li>
                    </ul>

                    <h3 style="color: #dc3545;">⚠️ 注意事项</h3>
                    <ul>
                        <li>请确保网络连接正常</li>
                        <li>API密钥请妥善保管</li>
                        <li>建议在填写前检查生成的内容</li>
                        <li>如遇问题可开启调试模式查看详情</li>
                    </ul>

                    <h3 style="color: #6f42c1;">🆘 常见问题</h3>
                    <p><strong>Q: AI生成内容不准确怎么办？</strong><br>
                    A: 可以调整AI温度参数，或者手动修改生成的内容。</p>

                    <p><strong>Q: 填写失败怎么办？</strong><br>
                    A: 检查网络连接和API配置，开启调试模式查看错误信息。</p>

                    <p><strong>Q: 如何自定义模板？</strong><br>
                    A: 可以修改脚本中的templates对象添加自定义模板。</p>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button onclick="this.parentElement.parentElement.parentElement.remove()"
                            style="padding: 10px 30px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
                        关闭
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(helpDialog);

        // 点击背景关闭
        helpDialog.onclick = (e) => {
            if (e.target === helpDialog) {
                helpDialog.remove();
            }
        };
    }

    // 数据备份功能
    function backupData() {
        if (!GM_getValue('backup-data', true)) return;

        const backupData = {
            timestamp: new Date().toISOString(),
            softwareName: document.getElementById('software-name')?.value || '',
            framework: document.getElementById('dev-framework')?.value || '',
            apiConfig: {
                url: GM_getValue('api-url', ''),
                model: GM_getValue('model-name', '')
            }
        };

        GM_setValue('backup-' + Date.now(), JSON.stringify(backupData));
        debugLog('数据已备份');
    }

    // 恢复数据功能
    function createBackupManager() {
        const backupButton = document.createElement('button');
        backupButton.textContent = '📁 备份管理';
        backupButton.style.cssText = `
            position: fixed;
            bottom: 80px;
            right: 20px;
            padding: 10px 15px;
            background: #fd7e14;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            z-index: 9999;
            font-size: 12px;
        `;

        backupButton.onclick = showBackupManager;
        document.body.appendChild(backupButton);
    }

    function showBackupManager() {
        const backups = [];

        // 获取所有备份
        for (let i = 0; i < 1000; i++) {
            const key = 'backup-' + (Date.now() - i * 86400000); // 检查最近1000天
            const backup = GM_getValue(key.toString());
            if (backup) {
                try {
                    backups.push({
                        key: key,
                        data: JSON.parse(backup)
                    });
                } catch (e) {
                    // 忽略解析错误
                }
            }
        }

        const backupDialog = document.createElement('div');
        backupDialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 10004;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        let backupList = '';
        backups.forEach((backup, index) => {
            const date = new Date(backup.data.timestamp).toLocaleString();
            backupList += `
                <div style="padding: 10px; border: 1px solid #ddd; margin-bottom: 10px; border-radius: 5px;">
                    <div><strong>时间:</strong> ${date}</div>
                    <div><strong>软件:</strong> ${backup.data.softwareName}</div>
                    <div><strong>框架:</strong> ${backup.data.framework}</div>
                    <button onclick="restoreBackup('${backup.key}')" style="margin-top: 5px; padding: 5px 10px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer;">
                        恢复
                    </button>
                    <button onclick="deleteBackup('${backup.key}')" style="margin-top: 5px; margin-left: 5px; padding: 5px 10px; background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer;">
                        删除
                    </button>
                </div>
            `;
        });

        backupDialog.innerHTML = `
            <div style="background: white; border-radius: 10px; padding: 20px; max-width: 500px; max-height: 70vh; overflow-y: auto;">
                <h3 style="margin-bottom: 20px; text-align: center;">📁 备份管理</h3>
                <div style="max-height: 400px; overflow-y: auto;">
                    ${backupList || '<p style="text-align: center; color: #666;">暂无备份数据</p>'}
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button onclick="this.parentElement.parentElement.parentElement.remove()"
                            style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        关闭
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(backupDialog);

        // 全局函数用于恢复和删除备份
        window.restoreBackup = (key) => {
            const backup = GM_getValue(key);
            if (backup) {
                try {
                    const data = JSON.parse(backup);
                    document.getElementById('software-name').value = data.softwareName;
                    document.getElementById('dev-framework').value = data.framework;
                    showStatus('备份已恢复！', 'success');
                    backupDialog.remove();
                } catch (e) {
                    showStatus('恢复失败！', 'error');
                }
            }
        };

        window.deleteBackup = (key) => {
            if (confirm('确定要删除这个备份吗？')) {
                GM_setValue(key, undefined);
                showStatus('备份已删除！', 'success');
                backupDialog.remove();
                showBackupManager(); // 重新显示
            }
        };
    }

    // 增强的错误处理
    function handleError(error, context) {
        debugLog(`错误 [${context}]: ${error.message}`);
        showStatus(`${context}失败: ${error.message}`, 'error');

        // 错误统计
        const errorCount = GM_getValue('error-count', 0) + 1;
        GM_setValue('error-count', errorCount);

        if (errorCount > 10) {
            if (confirm('检测到多次错误，是否重置所有设置？')) {
                resetSettings();
                GM_setValue('error-count', 0);
            }
        }
    }

    // 性能监控
    function createPerformanceMonitor() {
        const monitor = {
            startTime: Date.now(),
            operations: [],

            start(operation) {
                this.operations.push({
                    name: operation,
                    startTime: Date.now()
                });
            },

            end(operation) {
                const op = this.operations.find(o => o.name === operation && !o.endTime);
                if (op) {
                    op.endTime = Date.now();
                    op.duration = op.endTime - op.startTime;
                    debugLog(`性能: ${operation} 耗时 ${op.duration}ms`);
                }
            },

            getReport() {
                const totalTime = Date.now() - this.startTime;
                const completedOps = this.operations.filter(op => op.endTime);
                return {
                    totalTime,
                    operationCount: completedOps.length,
                    averageTime: completedOps.reduce((sum, op) => sum + op.duration, 0) / completedOps.length || 0,
                    operations: completedOps
                };
            }
        };

        window.performanceMonitor = monitor;
        return monitor;
    }

    // 主初始化函数
    function init() {
        try {
            console.log('[版权助手] 开始初始化');

            // 创建性能监控
            const monitor = createPerformanceMonitor();

            // 创建所有UI组件
            monitor.start('UI创建');
            createConfigPanel();
            createQuickActions();
            createTemplateManager();
            createProgressIndicator();
            createDebugPanel();
            createSettingsPanel();
            createMainMenu();
            createBackupManager();
            monitor.end('UI创建');

            // 等待DOM元素创建完成后绑定事件
            setTimeout(() => {
                try {
                    // 更新开始填表按钮事件
                    const startButton = document.getElementById('start-auto-fill');
                    if (startButton) {
                        startButton.onclick = enhancedAutoFill;
                        console.log('[版权助手] 开始填表按钮事件已绑定');
                    }

                    // 添加导入导出按钮
                    addImportExportButtons();

                    // 设置自动保存
                    setupAutoSave();

                    // 检查网络状态
                    checkNetworkStatus();

                    console.log('[版权助手] 所有组件初始化完成');
                } catch (error) {
                    console.error('[版权助手] 事件绑定失败:', error);
                }
            }, 100);

            // 自动备份数据
            setInterval(backupData, 60000); // 每分钟备份一次

            // 显示欢迎消息
            setTimeout(() => {
                showStatus('版权注册助手已就绪！点击右侧机器人图标开始使用', 'success');
            }, 1000);

        } catch (error) {
            console.error('[版权助手] 初始化失败:', error);
            if (typeof handleError === 'function') {
                handleError(error, '初始化');
            }
        }
    }

    // 等待页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // 页面卸载时的清理工作
    window.addEventListener('beforeunload', () => {
        try {
            // 保存当前状态
            backupData();

            // 性能报告
            if (window.performanceMonitor) {
                const report = window.performanceMonitor.getReport();
                debugLog(`性能报告: 总耗时${report.totalTime}ms, 操作${report.operationCount}次, 平均${Math.round(report.averageTime)}ms`);
                GM_setValue('last-performance', JSON.stringify(report));
            }

            debugLog('版权注册助手已安全退出');
        } catch (error) {
            console.error('清理工作失败:', error);
        }
    });

    // 键盘快捷键支持
    document.addEventListener('keydown', (e) => {
        // Ctrl + Shift + A: 开始自动填表
        if (e.ctrlKey && e.shiftKey && e.key === 'A') {
            e.preventDefault();
            enhancedAutoFill();
        }

        // Ctrl + Shift + C: 打开配置面板
        if (e.ctrlKey && e.shiftKey && e.key === 'C') {
            e.preventDefault();
            const configPanel = document.getElementById('ai-config-panel');
            configPanel.style.display = configPanel.style.display === 'none' ? 'block' : 'block';
        }

        // Ctrl + Shift + D: 切换调试模式
        if (e.ctrlKey && e.shiftKey && e.key === 'D') {
            e.preventDefault();
            const debugPanel = document.getElementById('debug-panel');
            debugPanel.style.display = debugPanel.style.display === 'none' ? 'block' : 'none';
        }

        // Ctrl + Shift + H: 显示帮助
        if (e.ctrlKey && e.shiftKey && e.key === 'H') {
            e.preventDefault();
            showHelpDialog();
        }

        // ESC: 关闭所有面板
        if (e.key === 'Escape') {
            const panels = ['ai-config-panel', 'template-panel', 'settings-panel', 'debug-panel', 'progress-panel'];
            panels.forEach(panelId => {
                const panel = document.getElementById(panelId);
                if (panel) panel.style.display = 'none';
            });

            // 关闭主菜单
            const menu = document.getElementById('main-menu');
            const menuButton = document.getElementById('main-menu-button');
            if (menu && menuButton) {
                menu.style.right = '-300px';
                menuButton.style.right = '0px';
            }
        }
    });

    // 自动保存功能
    function setupAutoSave() {
        const inputs = ['software-name', 'dev-framework', 'api-url', 'api-key', 'model-name'];

        inputs.forEach(inputId => {
            setTimeout(() => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.addEventListener('input', debounce(() => {
                        GM_setValue(inputId, input.value);
                        console.log(`[版权助手] 自动保存: ${inputId}`);
                    }, 1000));
                }
            }, 200);
        });
    }

    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 网络状态检测
    function checkNetworkStatus() {
        const statusIndicator = document.createElement('div');
        statusIndicator.id = 'network-status';
        statusIndicator.style.cssText = `
            position: fixed;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            z-index: 10005;
            transition: all 0.3s ease;
        `;

        function updateStatus() {
            if (navigator.onLine) {
                statusIndicator.textContent = '🟢 网络正常';
                statusIndicator.style.background = '#d4edda';
                statusIndicator.style.color = '#155724';
            } else {
                statusIndicator.textContent = '🔴 网络断开';
                statusIndicator.style.background = '#f8d7da';
                statusIndicator.style.color = '#721c24';
            }
        }

        updateStatus();
        document.body.appendChild(statusIndicator);

        window.addEventListener('online', updateStatus);
        window.addEventListener('offline', updateStatus);

        // 3秒后隐藏状态指示器
        setTimeout(() => {
            statusIndicator.style.opacity = '0';
            setTimeout(() => statusIndicator.remove(), 300);
        }, 3000);
    }

    // 表单验证功能
    function validateForm() {
        const requiredFields = [
            { id: 'software-name', name: '软件名称' },
            { id: 'dev-framework', name: '开发框架' },
            { id: 'api-url', name: 'API地址' },
            { id: 'api-key', name: 'API密钥' }
        ];

        const errors = [];

        requiredFields.forEach(field => {
            const element = document.getElementById(field.id);
            if (!element || !element.value.trim()) {
                errors.push(field.name);
            }
        });

        if (errors.length > 0) {
            showStatus(`请填写必填项: ${errors.join(', ')}`, 'error');
            return false;
        }

        // 验证API URL格式
        const apiUrl = document.getElementById('api-url').value;
        try {
            new URL(apiUrl);
        } catch {
            showStatus('API地址格式不正确', 'error');
            return false;
        }

        return true;
    }

    // 导出配置功能
    function exportConfig() {
        const config = {
            version: '1.0',
            timestamp: new Date().toISOString(),
            apiUrl: GM_getValue('api-url', ''),
            modelName: GM_getValue('model-name', ''),
            softwareName: GM_getValue('software-name', ''),
            devFramework: GM_getValue('dev-framework', ''),
            settings: {
                fillDelay: GM_getValue('fill-delay', 500),
                clickDelay: GM_getValue('click-delay', 1000),
                aiTemperature: GM_getValue('ai-temperature', 0.7),
                maxRetries: GM_getValue('max-retries', 3),
                autoScroll: GM_getValue('auto-scroll', true),
                debugMode: GM_getValue('debug-mode', false),
                backupData: GM_getValue('backup-data', true)
            }
        };

        const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `copyright-assistant-config-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);

        showStatus('配置已导出', 'success');
    }

    // 导入配置功能
    function importConfig() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';

        input.onchange = (e) => {
            const file = e.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const config = JSON.parse(e.target.result);

                    // 验证配置格式
                    if (!config.version) {
                        throw new Error('无效的配置文件格式');
                    }

                    // 导入配置
                    if (config.apiUrl) GM_setValue('api-url', config.apiUrl);
                    if (config.modelName) GM_setValue('model-name', config.modelName);
                    if (config.softwareName) GM_setValue('software-name', config.softwareName);
                    if (config.devFramework) GM_setValue('dev-framework', config.devFramework);

                    // 导入设置
                    if (config.settings) {
                        Object.keys(config.settings).forEach(key => {
                            const value = config.settings[key];
                            const settingKey = key.replace(/([A-Z])/g, '-$1').toLowerCase();
                            GM_setValue(settingKey, value);
                        });
                    }

                    // 重新加载配置到界面
                    loadConfig();
                    loadSettings();

                    showStatus('配置导入成功', 'success');

                } catch (error) {
                    showStatus('配置导入失败: ' + error.message, 'error');
                }
            };
            reader.readAsText(file);
        };

        input.click();
    }

    // 添加导入导出按钮到配置面板
    function addImportExportButtons() {
        const configPanel = document.getElementById('ai-config-panel');
        if (configPanel) {
            // 检查是否已经添加过按钮
            if (document.getElementById('export-config')) {
                return;
            }

            const buttonContainer = document.createElement('div');
            buttonContainer.style.cssText = 'display: flex; gap: 10px; margin-top: 10px;';

            buttonContainer.innerHTML = `
                <button id="export-config" style="flex: 1; padding: 8px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                    导出配置
                </button>
                <button id="import-config" style="flex: 1; padding: 8px; background: #fd7e14; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                    导入配置
                </button>
            `;

            // 在状态显示区域之前插入
            const statusDiv = document.getElementById('status');
            if (statusDiv) {
                configPanel.insertBefore(buttonContainer, statusDiv);

                // 绑定事件
                document.getElementById('export-config').onclick = exportConfig;
                document.getElementById('import-config').onclick = importConfig;
            }
        }
    }

    // 智能重试机制
    async function retryOperation(operation, maxRetries = 3, delay = 1000) {
        for (let i = 0; i < maxRetries; i++) {
            try {
                return await operation();
            } catch (error) {
                debugLog(`操作失败，第${i + 1}次重试: ${error.message}`);

                if (i === maxRetries - 1) {
                    throw error;
                }

                // 指数退避
                await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
            }
        }
    }

    // 增强的AI调用函数
    async function enhancedCallAI(prompt) {
        const maxRetries = GM_getValue('max-retries', 3);

        return retryOperation(async () => {
            const apiUrl = GM_getValue('api-url');
            const apiKey = GM_getValue('api-key');
            const modelName = GM_getValue('model-name');
            const temperature = GM_getValue('ai-temperature', 0.7);

            if (!apiUrl || !apiKey) {
                throw new Error('请先配置API地址和密钥');
            }

            return new Promise((resolve, reject) => {
                const startTime = Date.now();

                GM_xmlhttpRequest({
                    method: 'POST',
                    url: apiUrl,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`,
                        'User-Agent': 'Copyright-Assistant/1.0'
                    },
                    data: JSON.stringify({
                        model: modelName,
                        messages: [
                            {
                                role: 'system',
                                content: '你是一个专业的软件版权注册助手，请根据用户提供的信息生成准确、专业的版权注册表单内容。'
                            },
                            {
                                role: 'user',
                                content: prompt
                            }
                        ],
                        temperature: temperature,
                        max_tokens: 2000
                    }),
                    timeout: 30000, // 30秒超时
                    onload: function(response) {
                        const duration = Date.now() - startTime;
                        debugLog(`AI调用完成，耗时: ${duration}ms`);

                        try {
                            const data = JSON.parse(response.responseText);

                            if (response.status !== 200) {
                                throw new Error(`API错误 ${response.status}: ${data.error?.message || '未知错误'}`);
                            }

                            if (data.choices && data.choices[0]) {
                                const content = data.choices[0].message.content;
                                debugLog(`AI响应长度: ${content.length}字符`);

                                // 尝试解析JSON
                                const jsonMatch = content.match(/\{[\s\S]*\}/);
                                if (jsonMatch) {
                                    const result = JSON.parse(jsonMatch[0]);

                                    // 验证结果完整性
                                    const requiredFields = ['devHardware', 'runHardware', 'devOS', 'devTools', 'runPlatform', 'supportSoftware', 'purpose', 'industry', 'mainFunction', 'techFeatures'];
                                    const missingFields = requiredFields.filter(field => !result[field]);

                                    if (missingFields.length > 0) {
                                        throw new Error(`AI响应缺少字段: ${missingFields.join(', ')}`);
                                    }

                                    resolve(result);
                                } else {
                                    throw new Error('AI返回格式错误，未找到JSON数据');
                                }
                            } else {
                                throw new Error('AI响应格式错误，缺少choices字段');
                            }
                        } catch (error) {
                            reject(new Error('解析AI响应失败: ' + error.message));
                        }
                    },
                    onerror: function(error) {
                        reject(new Error('网络请求失败: ' + error.message));
                    },
                    ontimeout: function() {
                        reject(new Error('请求超时，请检查网络连接'));
                    }
                });
            });
        }, maxRetries);
    }

    // 智能表单填写函数
    async function smartFillField(selector, value, options = {}) {
        const {
            isTextarea = false,
            scrollIntoView = true,
            validateAfterFill = true,
            retries = 3
        } = options;

        return retryOperation(async () => {
            const element = await waitForElement(selector, 10000);

            if (scrollIntoView && GM_getValue('auto-scroll', true)) {
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // 清空现有内容
            element.value = '';
            element.dispatchEvent(new Event('input', { bubbles: true }));

            // 模拟人工输入
            if (GM_getValue('human-like-typing', false)) {
                await simulateTyping(element, value);
            } else {
                element.value = value;
            }

            // 触发各种事件确保兼容性
            const events = ['input', 'change', 'blur', 'keyup'];
            events.forEach(eventType => {
                element.dispatchEvent(new Event(eventType, { bubbles: true }));
            });

            // 验证填写是否成功
            if (validateAfterFill) {
                await new Promise(resolve => setTimeout(resolve, 200));
                if (element.value !== value) {
                    throw new Error(`字段填写验证失败: ${selector}`);
                }
            }

            const delay = GM_getValue('fill-delay', 500);
            await new Promise(resolve => setTimeout(resolve, delay));

            debugLog(`成功填写字段: ${selector} = ${value.substring(0, 50)}...`);
            return true;

        }, retries);
    }

    // 模拟人工打字
    async function simulateTyping(element, text) {
        element.focus();

        for (let i = 0; i < text.length; i++) {
            element.value += text[i];
            element.dispatchEvent(new Event('input', { bubbles: true }));

            // 随机延迟模拟真实打字速度
            const delay = Math.random() * 100 + 50;
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    // 智能按钮点击函数
    async function smartClickButton(selector, options = {}) {
        const {
            waitAfterClick = true,
            scrollIntoView = true,
            retries = 3
        } = options;

        return retryOperation(async () => {
            const element = await waitForElement(selector, 10000);

            if (scrollIntoView && GM_getValue('auto-scroll', true)) {
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // 确保元素可点击
            if (element.disabled) {
                throw new Error(`按钮被禁用: ${selector}`);
            }

            // 模拟鼠标悬停
            element.dispatchEvent(new MouseEvent('mouseover', { bubbles: true }));
            await new Promise(resolve => setTimeout(resolve, 100));

            // 点击元素
            element.click();

            // 触发额外事件确保兼容性
            element.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
            element.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));

            if (waitAfterClick) {
                const delay = GM_getValue('click-delay', 1000);
                await new Promise(resolve => setTimeout(resolve, delay));
            }

            debugLog(`成功点击按钮: ${selector}`);
            return true;

        }, retries);
    }

    // 页面状态检测
    function detectPageState() {
        const indicators = {
            hasBasicForm: () => document.querySelector("input[placeholder='请输入软件全称']"),
            hasTypeSelection: () => document.querySelector("div[contains(text(),'应用软件')]"),
            hasDetailForm: () => document.querySelector("textarea[placeholder='请输入...']"),
            isLoading: () => document.querySelector('.loading, .spinner, [class*="loading"]'),
            hasError: () => document.querySelector('.error, .alert-danger, [class*="error"]')
        };

        const state = {};
        Object.keys(indicators).forEach(key => {
            state[key] = indicators[key]();
        });

        debugLog(`页面状态: ${JSON.stringify(state, null, 2)}`);
        return state;
    }

    // 完全重写的自动填表函数
    async function ultimateAutoFill() {
        const monitor = window.performanceMonitor;
        monitor?.start('完整填表流程');

        try {
            // 验证配置
            if (!validateForm()) {
                return;
            }

            updateProgress(0, 15, '初始化填表流程...');

            const softwareName = document.getElementById('software-name').value;
            const framework = document.getElementById('dev-framework').value;

            // 检测当前页面状态
            const pageState = detectPageState();

            // 第一阶段：基本信息填写
            if (pageState.hasBasicForm) {
                updateProgress(1, 15, '填写软件基本信息...');

                await smartFillField("//input[@placeholder='请输入软件全称']", softwareName, {
                    validateAfterFill: true
                });

                updateProgress(2, 15, '填写版本号...');
                await smartFillField("//input[@placeholder='请输入版本号']", "v1.0", {
                    validateAfterFill: true
                });

                updateProgress(3, 15, '进入下一步...');
                await smartClickButton("//button[contains(text(),'下一步')]");

                // 等待页面加载
                await waitForPageLoad();
            }

            // 第二阶段：软件类型选择
            updateProgress(4, 15, '选择软件类型...');
            await smartClickButton("//div[contains(text(),'应用软件')]");

            updateProgress(5, 15, '选择完成时间...');
            await smartClickButton("//span[contains(text(),'今天')]");

            updateProgress(6, 15, '进入详细信息页面...');
            await smartClickButton("//button[contains(text(),'下一步')]");

            // 等待详细表单加载
            await waitForPageLoad();

            // 第三阶段：AI内容生成
            updateProgress(7, 15, '调用AI生成专业内容...');
            const prompt = generateEnhancedPrompt(softwareName, framework);
            const aiResult = await enhancedCallAI(prompt);

            // 第四阶段：详细信息填写
            const detailFields = [
                {
                    selector: "(//textarea[@placeholder='请输入...'])[1]",
                    value: aiResult.devHardware,
                    name: '开发硬件环境'
                },
                {
                    selector: "(//textarea[@placeholder='请输入...'])[2]",
                    value: aiResult.runHardware,
                    name: '运行硬件环境'
                },
                {
                    selector: "(//textarea[@class='large blur'])[1]",
                    value: aiResult.devOS,
                    name: '开发操作系统'
                },
                {
                    selector: "(//textarea[@placeholder='请输入...'])[4]",
                    value: aiResult.devTools,
                    name: '开发工具'
                },
                {
                    selector: "(//textarea[@placeholder='请输入...'])[5]",
                    value: aiResult.runPlatform,
                    name: '运行平台'
                },
                {
                    selector: "(//textarea[@class='large blur'])[2]",
                    value: aiResult.supportSoftware,
                    name: '支撑环境'
                },
                {
                    selector: "(//textarea[@class='large blur'])[3]",
                    value: aiResult.purpose,
                    name: '开发目的'
                },
                {
                    selector: "(//textarea[@placeholder='请输入...'])[8]",
                    value: aiResult.industry,
                    name: '面向领域'
                },
                {
                    selector: "(//textarea[@placeholder='请输入...'])[9]",
                    value: aiResult.mainFunction,
                    name: '主要功能'
                },
                {
                    selector: "(//textarea[@class='large blur'])[4]",
                    value: aiResult.techFeatures,
                    name: '技术特点'
                }
            ];

            for (let i = 0; i < detailFields.length; i++) {
                const field = detailFields[i];
                const progress = 8 + (i / detailFields.length) * 6;

                updateProgress(progress, 15, `填写${field.name}...`);

                await smartFillField(field.selector, field.value, {
                    isTextarea: true,
                    validateAfterFill: true
                });
            }

            updateProgress(14, 15, '验证填写结果...');

            // 验证所有字段是否填写成功
            const validationResults = await validateAllFields(detailFields);
            const failedFields = validationResults.filter(r => !r.success);

            if (failedFields.length > 0) {
                debugLog(`部分字段填写失败: ${failedFields.map(f => f.name).join(', ')}`);
                showStatus(`部分字段填写失败，请手动检查`, 'error');
            }

            updateProgress(15, 15, '自动填表完成！');

            // 保存填写记录
            saveFillRecord(softwareName, framework, aiResult);

            showStatus('🎉 所有字段填写完成！请检查内容后提交', 'success');

            // 显示完成统计
            showCompletionStats(validationResults);

        } catch (error) {
            handleError(error, '自动填表');
        } finally {
            monitor?.end('完整填表流程');

            // 3秒后自动关闭进度面板
            setTimeout(() => {
                const progressPanel = document.getElementById('progress-panel');
                if (progressPanel) progressPanel.style.display = 'none';
            }, 3000);
        }
    }

    // 等待页面加载完成
    async function waitForPageLoad(timeout = 10000) {
        const startTime = Date.now();

        while (Date.now() - startTime < timeout) {
            const pageState = detectPageState();

            if (!pageState.isLoading) {
                await new Promise(resolve => setTimeout(resolve, 1000)); // 额外等待1秒确保稳定
                return;
            }

            await new Promise(resolve => setTimeout(resolve, 500));
        }

        debugLog('页面加载超时，继续执行');
    }

    // 增强的提示词生成
    function generateEnhancedPrompt(softwareName, framework) {
        const currentDate = new Date().toLocaleDateString('zh-CN');

        return `你是一个专业的软件版权注册专家。请根据以下信息生成符合中国版权局要求的软件版权注册表单内容：

软件名称：${softwareName}
开发框架/技术栈：${framework}
填表日期：${currentDate}

请严格按照以下要求生成内容：

1. 开发的硬件环境（40-60字）：描述开发时使用的计算机硬件配置
2. 运行的硬件环境（40-60字）：描述软件运行所需的最低硬件要求
3. 开发该软件的操作系统（80-120字）：详细说明开发环境的操作系统
4. 软件开发环境/开发工具（40-60字）：列出主要的开发工具和IDE
5. 该软件的运行平台/操作系统（40-60字）：说明软件支持的运行平台
6. 软件运行支撑环境/支持软件（80-120字）：描述运行时依赖的软件环境
7. 开发目的（80-120字）：说明开发这个软件的目标和意义
8. 面向领域/行业（20-30字）：简洁说明软件的应用领域
9. 软件的主要功能（120-180字）：详细描述软件的核心功能模块
10. 软件的技术特点（80-120字）：突出软件的技术亮点和创新点

要求：
- 内容要专业、准确、符合版权注册规范
- 避免使用过于技术化的术语，要通俗易懂
- 字数要严格控制在指定范围内
- 内容要与提供的技术栈相匹配
- 体现软件的实用性和创新性

请以标准JSON格式返回，确保所有字段都有内容：

{
  "devHardware": "开发硬件环境内容",
  "runHardware": "运行硬件环境内容",
  "devOS": "开发操作系统内容",
  "devTools": "开发工具内容",
  "runPlatform": "运行平台内容",
  "supportSoftware": "支撑环境内容",
  "purpose": "开发目的内容",
  "industry": "面向领域内容",
  "mainFunction": "主要功能内容",
  "techFeatures": "技术特点内容"
}`;
    }

    // 验证所有字段
    async function validateAllFields(fields) {
        const results = [];

        for (const field of fields) {
            try {
                const element = await waitForElement(field.selector, 2000);
                const success = element.value.trim() === field.value.trim();

                results.push({
                    name: field.name,
                    selector: field.selector,
                    success: success,
                    actualValue: element.value.trim(),
                    expectedValue: field.value.trim()
                });

            } catch (error) {
                results.push({
                    name: field.name,
                    selector: field.selector,
                    success: false,
                    error: error.message
                });
            }
        }

        return results;
    }

    // 保存填写记录
    function saveFillRecord(softwareName, framework, aiResult) {
        const record = {
            timestamp: new Date().toISOString(),
            softwareName,
            framework,
            aiResult,
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        const records = JSON.parse(GM_getValue('fill-records', '[]'));
        records.unshift(record); // 最新记录在前

        // 只保留最近50条记录
        if (records.length > 50) {
            records.splice(50);
        }

        GM_setValue('fill-records', JSON.stringify(records));
        debugLog('填写记录已保存');
    }

    // 显示完成统计
    function showCompletionStats(validationResults) {
        const successCount = validationResults.filter(r => r.success).length;
        const totalCount = validationResults.length;
        const successRate = Math.round((successCount / totalCount) * 100);

        const statsDialog = document.createElement('div');
        statsDialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 10005;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        const failedFields = validationResults.filter(r => !r.success);
        let failedFieldsHtml = '';

        if (failedFields.length > 0) {
            failedFieldsHtml = `
                <div style="margin-top: 15px; padding: 10px; background: #f8d7da; border-radius: 5px;">
                    <h4 style="color: #721c24; margin-bottom: 10px;">需要手动检查的字段：</h4>
                    <ul style="margin: 0; padding-left: 20px; color: #721c24;">
                        ${failedFields.map(f => `<li>${f.name}${f.error ? ` (${f.error})` : ''}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        statsDialog.innerHTML = `
            <div style="background: white; border-radius: 10px; padding: 30px; max-width: 500px; text-align: center;">
                <div style="font-size: 48px; margin-bottom: 20px;">
                    ${successRate === 100 ? '🎉' : successRate >= 80 ? '✅' : '⚠️'}
                </div>

                <h2 style="color: #28a745; margin-bottom: 20px;">填表完成报告</h2>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div style="padding: 15px; background: #d4edda; border-radius: 8px;">
                        <div style="font-size: 24px; font-weight: bold; color: #155724;">${successCount}</div>
                        <div style="color: #155724;">成功填写</div>
                    </div>
                    <div style="padding: 15px; background: ${failedFields.length > 0 ? '#f8d7da' : '#d4edda'}; border-radius: 8px;">
                        <div style="font-size: 24px; font-weight: bold; color: ${failedFields.length > 0 ? '#721c24' : '#155724'};">${failedFields.length}</div>
                        <div style="color: ${failedFields.length > 0 ? '#721c24' : '#155724'};">需要检查</div>
                    </div>
                </div>

                <div style="margin-bottom: 20px;">
                    <div style="font-size: 18px; font-weight: bold; color: #333;">成功率: ${successRate}%</div>
                    <div style="width: 100%; height: 10px; background: #e9ecef; border-radius: 5px; margin-top: 10px; overflow: hidden;">
                        <div style="height: 100%; background: linear-gradient(90deg, #28a745, #20c997); width: ${successRate}%; transition: width 1s ease;"></div>
                    </div>
                </div>

                ${failedFieldsHtml}

                <div style="display: flex; gap: 10px; margin-top: 20px;">
                    <button onclick="this.parentElement.parentElement.parentElement.remove()"
                            style="flex: 1; padding: 12px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        确定
                    </button>
                    <button onclick="showFillHistory()"
                            style="flex: 1; padding: 12px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        查看历史
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(statsDialog);

        // 3秒后自动关闭（如果100%成功）
        if (successRate === 100) {
            setTimeout(() => {
                if (statsDialog.parentNode) {
                    statsDialog.remove();
                }
            }, 5000);
        }
    }

    // 显示填写历史
    function showFillHistory() {
        const records = JSON.parse(GM_getValue('fill-records', '[]'));

        const historyDialog = document.createElement('div');
        historyDialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 10006;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        let recordsHtml = '';
        if (records.length === 0) {
            recordsHtml = '<p style="text-align: center; color: #666;">暂无填写记录</p>';
        } else {
            recordsHtml = records.slice(0, 10).map((record, index) => {
                const date = new Date(record.timestamp).toLocaleString('zh-CN');
                return `
                    <div style="padding: 15px; border: 1px solid #ddd; border-radius: 8px; margin-bottom: 10px; background: ${index === 0 ? '#f8f9fa' : 'white'};">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <strong style="color: #007bff;">${record.softwareName}</strong>
                            <span style="font-size: 12px; color: #666;">${date}</span>
                        </div>
                        <div style="font-size: 14px; color: #666;">
                            <div>框架: ${record.framework}</div>
                            <div style="margin-top: 5px;">
                                <button onclick="reuseFillData('${index}')" style="padding: 5px 10px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 12px;">
                                    重用此配置
                                </button>
                                <button onclick="deleteFillRecord('${index}')" style="margin-left: 5px; padding: 5px 10px; background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 12px;">
                                    删除
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        historyDialog.innerHTML = `
            <div style="background: white; border-radius: 10px; padding: 20px; max-width: 600px; max-height: 80vh; overflow-y: auto;">
                <h3 style="margin-bottom: 20px; text-align: center; color: #333;">📋 填写历史记录</h3>

                <div style="max-height: 500px; overflow-y: auto;">
                    ${recordsHtml}
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button onclick="this.parentElement.parentElement.parentElement.remove()"
                            style="padding: 10px 30px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        关闭
                    </button>
                    ${records.length > 0 ? `
                        <button onclick="clearFillHistory()"
                                style="margin-left: 10px; padding: 10px 30px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            清空历史
                        </button>
                    ` : ''}
                </div>
            </div>
        `;

        document.body.appendChild(historyDialog);

        // 全局函数
        window.reuseFillData = (index) => {
            const records = JSON.parse(GM_getValue('fill-records', '[]'));
            const record = records[index];

            if (record) {
                document.getElementById('software-name').value = record.softwareName;
                document.getElementById('dev-framework').value = record.framework;
                saveConfig();
                showStatus('配置已恢复！', 'success');
                historyDialog.remove();
            }
        };

        window.deleteFillRecord = (index) => {
            if (confirm('确定要删除这条记录吗？')) {
                const records = JSON.parse(GM_getValue('fill-records', '[]'));
                records.splice(index, 1);
                GM_setValue('fill-records', JSON.stringify(records));
                historyDialog.remove();
                showFillHistory(); // 重新显示
            }
        };

        window.clearFillHistory = () => {
            if (confirm('确定要清空所有历史记录吗？')) {
                GM_setValue('fill-records', '[]');
                showStatus('历史记录已清空', 'success');
                historyDialog.remove();
            }
        };
    }

    // 创建浮动工具栏
    function createFloatingToolbar() {
        const toolbar = document.createElement('div');
        toolbar.id = 'floating-toolbar';
        toolbar.style.cssText = `
            position: fixed;
            bottom: 100px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            z-index: 9998;
        `;

        const tools = [
            { id: 'quick-fill', text: '🚀', title: '快速填表', color: '#28a745' },
            { id: 'ai-enhance', text: '🤖', title: 'AI优化', color: '#007bff' },
            { id: 'validate-form', text: '✅', title: '验证表单', color: '#17a2b8' },
            { id: 'save-draft', text: '💾', title: '保存草稿', color: '#ffc107' },
            { id: 'load-draft', text: '📂', title: '加载草稿', color: '#fd7e14' }
        ];

        tools.forEach(tool => {
            const button = document.createElement('button');
            button.id = tool.id;
            button.textContent = tool.text;
            button.title = tool.title;
            button.style.cssText = `
                width: 45px;
                height: 45px;
                border: none;
                border-radius: 50%;
                background: ${tool.color};
                color: white;
                font-size: 18px;
                cursor: pointer;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
            `;

            button.onmouseover = () => {
                button.style.transform = 'scale(1.1)';
                button.style.boxShadow = '0 4px 12px rgba(0,0,0,0.3)';
            };

            button.onmouseout = () => {
                button.style.transform = 'scale(1)';
                button.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
            };

            toolbar.appendChild(button);
        });

        document.body.appendChild(toolbar);

        // 绑定工具栏事件
        document.getElementById('quick-fill').onclick = ultimateAutoFill;
        document.getElementById('ai-enhance').onclick = enhanceCurrentContent;
        document.getElementById('validate-form').onclick = validateCurrentForm;
        document.getElementById('save-draft').onclick = saveDraft;
        document.getElementById('load-draft').onclick = loadDraft;
    }

    // AI内容优化功能
    async function enhanceCurrentContent() {
        try {
            showStatus('正在优化当前内容...', 'info');

            const textareas = document.querySelectorAll('textarea');
            const enhancedContents = [];

            for (const textarea of textareas) {
                if (textarea.value.trim()) {
                    const prompt = `请优化以下软件版权注册表单内容，使其更加专业和规范：

原内容：${textarea.value}

要求：
1. 保持原意不变
2. 语言更加专业规范
3. 符合版权注册要求
4. 字数控制在合理范围内

请直接返回优化后的内容，不要添加额外说明。`;

                    const response = await enhancedCallAI(prompt);
                    enhancedContents.push({
                        element: textarea,
                        original: textarea.value,
                        enhanced: response.content || response
                    });
                }
            }

            if (enhancedContents.length > 0) {
                showEnhancementPreview(enhancedContents);
            } else {
                showStatus('没有找到可优化的内容', 'error');
            }

        } catch (error) {
            handleError(error, 'AI内容优化');
        }
    }

    // 显示优化预览
    function showEnhancementPreview(contents) {
        const previewDialog = document.createElement('div');
        previewDialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 10006;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        let contentHtml = contents.map((content, index) => `
            <div style="margin-bottom: 20px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
                <div style="background: #f8f9fa; padding: 10px; font-weight: bold; border-bottom: 1px solid #ddd;">
                    字段 ${index + 1}
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0;">
                    <div style="padding: 15px; border-right: 1px solid #ddd;">
                        <h5 style="margin-bottom: 10px; color: #dc3545;">原内容：</h5>
                        <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 14px; line-height: 1.4; max-height: 150px; overflow-y: auto;">
                            ${content.original}
                        </div>
                    </div>
                    <div style="padding: 15px;">
                        <h5 style="margin-bottom: 10px; color: #28a745;">优化后：</h5>
                        <div style="background: #d4edda; padding: 10px; border-radius: 4px; font-size: 14px; line-height: 1.4; max-height: 150px; overflow-y: auto;">
                            ${content.enhanced}
                        </div>
                    </div>
                </div>
                <div style="padding: 10px; background: #f8f9fa; text-align: center;">
                    <label style="margin-right: 15px;">
                        <input type="checkbox" id="enhance-${index}" checked> 应用此优化
                    </label>
                </div>
            </div>
        `).join('');

        previewDialog.innerHTML = `
            <div style="background: white; border-radius: 10px; padding: 20px; max-width: 900px; max-height: 80vh; overflow-y: auto;">
                <h3 style="margin-bottom: 20px; text-align: center; color: #333;">🤖 AI内容优化预览</h3>

                <div style="margin-bottom: 20px;">
                    ${contentHtml}
                </div>

                <div style="text-align: center; display: flex; gap: 10px; justify-content: center;">
                    <button id="apply-enhancements" style="padding: 12px 24px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        应用选中的优化
                    </button>
                    <button onclick="this.parentElement.parentElement.parentElement.remove()"
                            style="padding: 12px 24px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        取消
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(previewDialog);

        // 应用优化
        document.getElementById('apply-enhancements').onclick = () => {
            let appliedCount = 0;

            contents.forEach((content, index) => {
                const checkbox = document.getElementById(`enhance-${index}`);
                if (checkbox && checkbox.checked) {
                    content.element.value = content.enhanced;
                    content.element.dispatchEvent(new Event('input', { bubbles: true }));
                    content.element.dispatchEvent(new Event('change', { bubbles: true }));
                    appliedCount++;
                }
            });

            previewDialog.remove();
            showStatus(`已应用 ${appliedCount} 项优化`, 'success');
        };
    }

    // 验证当前表单
    async function validateCurrentForm() {
        try {
            showStatus('正在验证表单...', 'info');

            const textareas = document.querySelectorAll('textarea');
            const inputs = document.querySelectorAll('input[type="text"]');
            const allFields = [...textareas, ...inputs];

            const validationResults = [];

            for (const field of allFields) {
                const value = field.value.trim();
                const placeholder = field.placeholder || '未知字段';

                let issues = [];

                // 基本验证
                if (!value) {
                    issues.push('字段为空');
                } else {
                    // 长度验证
                    if (value.length < 10) {
                        issues.push('内容过短');
                    }
                    if (value.length > 500) {
                        issues.push('内容过长');
                    }

                    // 内容质量验证
                    if (value.includes('TODO') || value.includes('待填写')) {
                        issues.push('包含占位符内容');
                    }

                    // 重复内容检测
                    const duplicates = allFields.filter(f => f !== field && f.value.trim() === value);
                    if (duplicates.length > 0) {
                        issues.push('与其他字段内容重复');
                    }
                }

                validationResults.push({
                    field: field,
                    placeholder: placeholder,
                    value: value,
                    issues: issues,
                    isValid: issues.length === 0
                });
            }

            showValidationResults(validationResults);

        } catch (error) {
            handleError(error, '表单验证');
        }
    }

    // 显示验证结果
    function showValidationResults(results) {
        const validCount = results.filter(r => r.isValid).length;
        const totalCount = results.length;
        const validRate = Math.round((validCount / totalCount) * 100);

        const resultDialog = document.createElement('div');
        resultDialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 10006;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        const issueFields = results.filter(r => !r.isValid);
        let issuesHtml = '';

        if (issueFields.length > 0) {
            issuesHtml = issueFields.map(field => `
                <div style="padding: 10px; border: 1px solid #dc3545; border-radius: 5px; margin-bottom: 10px; background: #f8d7da;">
                    <div style="font-weight: bold; color: #721c24; margin-bottom: 5px;">
                        ${field.placeholder}
                    </div>
                    <div style="font-size: 14px; color: #721c24;">
                        问题: ${field.issues.join(', ')}
                    </div>
                    ${field.value ? `
                        <div style="margin-top: 5px; font-size: 12px; color: #666; max-height: 60px; overflow-y: auto;">
                            内容预览: ${field.value.substring(0, 100)}${field.value.length > 100 ? '...' : ''}
                        </div>
                    ` : ''}
                </div>
            `).join('');
        }

        resultDialog.innerHTML = `
            <div style="background: white; border-radius: 10px; padding: 20px; max-width: 600px; max-height: 80vh; overflow-y: auto;">
                <h3 style="margin-bottom: 20px; text-align: center; color: #333;">✅ 表单验证结果</h3>

                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="font-size: 48px; margin-bottom: 10px;">
                        ${validRate === 100 ? '🎉' : validRate >= 80 ? '✅' : validRate >= 60 ? '⚠️' : '❌'}
                    </div>
                    <div style="font-size: 24px; font-weight: bold; color: ${validRate >= 80 ? '#28a745' : validRate >= 60 ? '#ffc107' : '#dc3545'};">
                        ${validRate}% 通过验证
                    </div>
                    <div style="color: #666;">
                        ${validCount}/${totalCount} 个字段验证通过
                    </div>
                </div>

                ${issuesHtml ? `
                    <div style="margin-bottom: 20px;">
                        <h4 style="color: #dc3545; margin-bottom: 15px;">需要修正的问题：</h4>
                        <div style="max-height: 300px; overflow-y: auto;">
                            ${issuesHtml}
                        </div>
                    </div>
                ` : `
                    <div style="text-align: center; padding: 20px; background: #d4edda; border-radius: 8px; color: #155724;">
                        🎉 所有字段都通过了验证！
                    </div>
                `}

                <div style="text-align: center;">
                    <button onclick="this.parentElement.parentElement.parentElement.remove()"
                            style="padding: 10px 30px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        确定
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(resultDialog);
    }

    // 保存草稿
    function saveDraft() {
        try {
            const draftData = {
                timestamp: new Date().toISOString(),
                url: window.location.href,
                fields: {}
            };

            // 收集所有表单数据
            const inputs = document.querySelectorAll('input[type="text"], textarea');
            inputs.forEach((input, index) => {
                if (input.value.trim()) {
                    draftData.fields[`field_${index}`] = {
                        value: input.value,
                        placeholder: input.placeholder || '',
                        tagName: input.tagName.toLowerCase()
                    };
                }
            });

            // 收集配置数据
            draftData.config = {
                softwareName: document.getElementById('software-name')?.value || '',
                framework: document.getElementById('dev-framework')?.value || ''
            };

            const draftKey = `draft_${Date.now()}`;
            GM_setValue(draftKey, JSON.stringify(draftData));

            showStatus('草稿已保存', 'success');
            debugLog(`草稿已保存: ${draftKey}`);

        } catch (error) {
            handleError(error, '保存草稿');
        }
    }

    // 加载草稿
    function loadDraft() {
        try {
            const drafts = [];

            // 获取所有草稿
            for (let i = 0; i < 100; i++) {
                const key = `draft_${Date.now() - i * 86400000}`;
                const draft = GM_getValue(key);
                if (draft) {
                    try {
                        drafts.push({
                            key: key,
                            data: JSON.parse(draft)
                        });
                    } catch (e) {
                        // 忽略解析错误
                    }
                }
            }

            if (drafts.length === 0) {
                showStatus('没有找到草稿', 'error');
                return;
            }

            showDraftSelector(drafts);

        } catch (error) {
            handleError(error, '加载草稿');
        }
    }

    // 显示草稿选择器
    function showDraftSelector(drafts) {
        const selectorDialog = document.createElement('div');
        selectorDialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 10006;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        const draftsHtml = drafts.map((draft, index) => {
            const date = new Date(draft.data.timestamp).toLocaleString('zh-CN');
            const fieldCount = Object.keys(draft.data.fields).length;
            const softwareName = draft.data.config?.softwareName || '未知软件';

            return `
                <div style="padding: 15px; border: 1px solid #ddd; border-radius: 8px; margin-bottom: 10px; cursor: pointer; transition: background 0.2s;"
                     onclick="loadDraftData('${draft.key}')"
                     onmouseover="this.style.background='#f8f9fa'"
                     onmouseout="this.style.background='white'">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <strong style="color: #007bff;">${softwareName}</strong>
                        <span style="font-size: 12px; color: #666;">${date}</span>
                    </div>
                    <div style="font-size: 14px; color: #666;">
                        包含 ${fieldCount} 个字段
                    </div>
                    <div style="margin-top: 8px;">
                        <button onclick="event.stopPropagation(); deleteDraft('${draft.key}')"
                                style="padding: 4px 8px; background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 12px;">
                            删除
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        selectorDialog.innerHTML = `
            <div style="background: white; border-radius: 10px; padding: 20px; max-width: 500px; max-height: 70vh; overflow-y: auto;">
                <h3 style="margin-bottom: 20px; text-align: center; color: #333;">📂 选择草稿</h3>

                <div style="max-height: 400px; overflow-y: auto;">
                    ${draftsHtml}
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button onclick="this.parentElement.parentElement.parentElement.remove()"
                            style="padding: 10px 30px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        取消
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(selectorDialog);

        // 全局函数
        window.loadDraftData = (draftKey) => {
            try {
                const draftData = JSON.parse(GM_getValue(draftKey));

                // 恢复配置
                if (draftData.config) {
                    if (draftData.config.softwareName) {
                        document.getElementById('software-name').value = draftData.config.softwareName;
                    }
                    if (draftData.config.framework) {
                        document.getElementById('dev-framework').value = draftData.config.framework;
                    }
                }

                // 恢复表单字段（这里需要智能匹配）
                const currentInputs = document.querySelectorAll('input[type="text"], textarea');
                const draftFields = Object.values(draftData.fields);

                // 简单的字段匹配策略
                draftFields.forEach((draftField, index) => {
                    if (currentInputs[index]) {
                        currentInputs[index].value = draftField.value;
                        currentInputs[index].dispatchEvent(new Event('input', { bubbles: true }));
                        currentInputs[index].dispatchEvent(new Event('change', { bubbles: true }));
                    }
                });

                selectorDialog.remove();
                showStatus('草稿已加载', 'success');

            } catch (error) {
                showStatus('加载草稿失败: ' + error.message, 'error');
            }
        };

        window.deleteDraft = (draftKey) => {
            if (confirm('确定要删除这个草稿吗？')) {
                GM_setValue(draftKey, undefined);
                showStatus('草稿已删除', 'success');
                selectorDialog.remove();
                loadDraft(); // 重新显示
            }
        };
    }

    // 创建状态监控面板
    function createStatusMonitor() {
        const monitor = document.createElement('div');
        monitor.id = 'status-monitor';
        monitor.style.cssText = `
            position: fixed;
            top: 100px;
            left: 20px;
            width: 250px;
            background: rgba(0,0,0,0.8);
            color: white;
            border-radius: 8px;
            padding: 15px;
            z-index: 9997;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            display: none;
        `;

        monitor.innerHTML = `
            <div style="margin-bottom: 10px; font-weight: bold; color: #4fd1c7;">
                📊 系统状态监控
            </div>
            <div id="monitor-content">
                <div>CPU使用率: <span id="cpu-usage">0%</span></div>
                <div>内存使用: <span id="memory-usage">0MB</span></div>
                <div>网络状态: <span id="network-status">检测中...</span></div>
                <div>API调用: <span id="api-calls">0次</span></div>
                <div>填写进度: <span id="fill-progress">0%</span></div>
                <div>错误计数: <span id="error-count">0</span></div>
            </div>
            <button onclick="toggleStatusMonitor()" style="width: 100%; padding: 5px; background: #4a5568; color: white; border: none; border-radius: 3px; cursor: pointer; margin-top: 10px; font-size: 11px;">
                隐藏监控
            </button>
        `;

        document.body.appendChild(monitor);

        // 启动监控
        startStatusMonitoring();
    }

    // 启动状态监控
    function startStatusMonitoring() {
        let apiCallCount = 0;
        let errorCount = GM_getValue('error-count', 0);

        // 监控API调用
        const originalCallAI = window.enhancedCallAI || callAI;
        window.enhancedCallAI = async function(...args) {
            apiCallCount++;
            updateMonitorValue('api-calls', apiCallCount + '次');
            try {
                return await originalCallAI.apply(this, args);
            } catch (error) {
                errorCount++;
                updateMonitorValue('error-count', errorCount);
                throw error;
            }
        };

        // 定期更新状态
        setInterval(() => {
            updateSystemStatus();
        }, 2000);
    }

    // 更新系统状态
    function updateSystemStatus() {
        // 模拟CPU使用率（基于页面活动）
        const cpuUsage = Math.min(100, Math.random() * 30 + 10);
        updateMonitorValue('cpu-usage', Math.round(cpuUsage) + '%');

        // 估算内存使用
        const memoryUsage = performance.memory ?
            Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) :
            Math.round(Math.random() * 50 + 20);
        updateMonitorValue('memory-usage', memoryUsage + 'MB');

        // 网络状态
        updateMonitorValue('network-status', navigator.onLine ? '✅ 正常' : '❌ 断开');

        // 填写进度（基于已填写字段）
        const totalFields = document.querySelectorAll('input[type="text"], textarea').length;
        const filledFields = Array.from(document.querySelectorAll('input[type="text"], textarea'))
            .filter(field => field.value.trim()).length;
        const progress = totalFields > 0 ? Math.round((filledFields / totalFields) * 100) : 0;
        updateMonitorValue('fill-progress', progress + '%');
    }

    // 更新监控值
    function updateMonitorValue(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    }

    // 切换状态监控显示
    window.toggleStatusMonitor = function() {
        const monitor = document.getElementById('status-monitor');
        if (monitor) {
            monitor.style.display = monitor.style.display === 'none' ? 'block' : 'none';
        }
    };

    // 创建快捷键帮助
    function createShortcutHelp() {
        const helpButton = document.createElement('button');
        helpButton.textContent = '⌨️';
        helpButton.title = '快捷键帮助';
        helpButton.style.cssText = `
            position: fixed;
            bottom: 160px;
            right: 20px;
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: #6f42c1;
            color: white;
            font-size: 16px;
            cursor: pointer;
            z-index: 9999;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        `;

        helpButton.onclick = showShortcutHelp;
        document.body.appendChild(helpButton);
    }

    // 显示快捷键帮助
    function showShortcutHelp() {
        const helpDialog = document.createElement('div');
        helpDialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 10007;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        helpDialog.innerHTML = `
            <div style="background: white; border-radius: 10px; padding: 30px; max-width: 500px;">
                <h3 style="margin-bottom: 20px; text-align: center; color: #333;">⌨️ 快捷键帮助</h3>

                <div style="display: grid; gap: 15px; margin-bottom: 20px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                        <span>开始自动填表</span>
                        <kbd style="background: #e9ecef; padding: 4px 8px; border-radius: 3px; font-family: monospace;">Ctrl + Shift + A</kbd>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                        <span>打开配置面板</span>
                        <kbd style="background: #e9ecef; padding: 4px 8px; border-radius: 3px; font-family: monospace;">Ctrl + Shift + C</kbd>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                        <span>切换调试模式</span>
                        <kbd style="background: #e9ecef; padding: 4px 8px; border-radius: 3px; font-family: monospace;">Ctrl + Shift + D</kbd>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                        <span>显示帮助</span>
                        <kbd style="background: #e9ecef; padding: 4px 8px; border-radius: 3px; font-family: monospace;">Ctrl + Shift + H</kbd>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                        <span>关闭所有面板</span>
                        <kbd style="background: #e9ecef; padding: 4px 8px; border-radius: 3px; font-family: monospace;">ESC</kbd>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                        <span>保存草稿</span>
                        <kbd style="background: #e9ecef; padding: 4px 8px; border-radius: 3px; font-family: monospace;">Ctrl + S</kbd>
                    </div>
                </div>

                <div style="text-align: center;">
                    <button onclick="this.parentElement.parentElement.parentElement.remove()"
                            style="padding: 10px 30px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        关闭
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(helpDialog);
    }

    // 添加更多快捷键
    document.addEventListener('keydown', (e) => {
        // Ctrl + S: 保存草稿
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            saveDraft();
        }

        // Ctrl + Shift + V: 验证表单
        if (e.ctrlKey && e.shiftKey && e.key === 'V') {
            e.preventDefault();
            validateCurrentForm();
        }

        // Ctrl + Shift + E: AI优化
        if (e.ctrlKey && e.shiftKey && e.key === 'E') {
            e.preventDefault();
            enhanceCurrentContent();
        }

        // F1: 显示快捷键帮助
        if (e.key === 'F1') {
            e.preventDefault();
            showShortcutHelp();
        }
    });

    // 最终的初始化函数更新
    function finalInit() {
        try {
            debugLog('版权注册助手完整版启动');

            // 检查网络状态
            checkNetworkStatus();

            // 创建性能监控
            const monitor = createPerformanceMonitor();

            // 创建所有UI组件
            monitor.start('UI组件创建');
            createConfigPanel();
            createQuickActions();
            createTemplateManager();
            createProgressIndicator();
            createDebugPanel();
            createSettingsPanel();
            createMainMenu();
            createBackupManager();
            createFloatingToolbar();
            createStatusMonitor();
            createShortcutHelp();
            monitor.end('UI组件创建');

            // 添加导入导出功能
            setTimeout(() => {
                addImportExportButtons();
                setupAutoSave();
            }, 1000);

            // 更新主要按钮事件
            document.getElementById('start-auto-fill').onclick = ultimateAutoFill;

            // 启动自动备份
            setInterval(backupData, 60000);

            // 显示状态监控（如果启用调试模式）
            if (GM_getValue('debug-mode', false)) {
                document.getElementById('status-monitor').style.display = 'block';
            }

            debugLog('版权注册助手完整版初始化完成');

            // 显示欢迎消息
            setTimeout(() => {
                showStatus('🚀 版权注册助手完整版已就绪！', 'success');

                // 显示版本信息
                setTimeout(() => {
                    debugLog('版本: 1.0.0 | 功能: AI填表、智能验证、草稿管理、性能监控');
                }, 2000);
            }, 1000);

        } catch (error) {
            handleError(error, '完整版初始化');
        }
    }

    // 替换原来的init函数
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', finalInit);
    } else {
        finalInit();
    }

    // 添加版本信息到控制台
    console.log(`
    🤖 版权注册自动填表助手 v1.0.0
    ================================
    ✅ AI智能填表
    ✅ 表单验证
    ✅ 内容优化
    ✅ 草稿管理
    ✅ 性能监控
    ✅ 快捷键支持
    ✅ 模板管理
    ✅ 配置导入导出
    ✅ 调试模式
    ✅ 自动备份

    快捷键:
    Ctrl+Shift+A - 开始填表
    Ctrl+Shift+C - 配置面板
    Ctrl+Shift+D - 调试模式
    Ctrl+S - 保存草稿
    ESC - 关闭面板

    作者: CodeGeeX Assistant
    `);

    // 添加全局错误处理
    window.addEventListener('error', (event) => {
        debugLog(`全局错误: ${event.error?.message || event.message}`);
        console.error('版权助手错误:', event.error);
    });

    window.addEventListener('unhandledrejection', (event) => {
        debugLog(`未处理的Promise拒绝: ${event.reason}`);
        console.error('版权助手Promise错误:', event.reason);
    });

    // 添加页面可见性变化处理
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            debugLog('页面隐藏，暂停监控');
        } else {
            debugLog('页面显示，恢复监控');
            updateSystemStatus();
        }
    });

    // 添加在线状态变化处理
    window.addEventListener('online', () => {
        debugLog('网络连接恢复');
        showStatus('网络连接已恢复', 'success');
    });

    window.addEventListener('offline', () => {
        debugLog('网络连接断开');
        showStatus('网络连接已断开', 'error');
    });

    // 添加内存清理功能
    function cleanupMemory() {
        // 清理旧的备份数据
        const backupKeys = [];
        for (let i = 0; i < 1000; i++) {
            const key = 'backup-' + (Date.now() - i * 86400000);
            if (GM_getValue(key)) {
                backupKeys.push(key);
            }
        }

        // 只保留最近30天的备份
        if (backupKeys.length > 30) {
            backupKeys.slice(30).forEach(key => {
                GM_setValue(key, undefined);
            });
            debugLog(`清理了 ${backupKeys.length - 30} 个旧备份`);
        }

        // 清理旧的填写记录
        const records = JSON.parse(GM_getValue('fill-records', '[]'));
        if (records.length > 100) {
            const cleanedRecords = records.slice(0, 100);
            GM_setValue('fill-records', JSON.stringify(cleanedRecords));
            debugLog(`清理了 ${records.length - 100} 个旧记录`);
        }
    }

    // 定期清理内存（每小时执行一次）
    setInterval(cleanupMemory, 3600000);

    // 添加数据统计功能
    function getUsageStats() {
        const stats = {
            totalFills: JSON.parse(GM_getValue('fill-records', '[]')).length,
            totalErrors: GM_getValue('error-count', 0),
            totalBackups: 0,
            configSaves: GM_getValue('config-save-count', 0),
            lastUsed: GM_getValue('last-used', '从未使用'),
            version: '1.0.0'
        };

        // 统计备份数量
        for (let i = 0; i < 100; i++) {
            const key = 'backup-' + (Date.now() - i * 86400000);
            if (GM_getValue(key)) {
                stats.totalBackups++;
            }
        }

        return stats;
    }

    // 添加统计面板到主菜单
    function addStatsToMenu() {
        const menu = document.getElementById('main-menu');
        if (menu) {
            const statsButton = document.createElement('button');
            statsButton.textContent = '📊 使用统计';
            statsButton.style.cssText = `
                padding: 12px;
                background: #e83e8c;
                color: white;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                width: 100%;
                margin-top: 10px;
            `;

            statsButton.onclick = showUsageStats;
            menu.querySelector('div:last-child').appendChild(statsButton);
        }
    }

    // 显示使用统计
    function showUsageStats() {
        const stats = getUsageStats();

        const statsDialog = document.createElement('div');
        statsDialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 10007;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        statsDialog.innerHTML = `
            <div style="background: white; border-radius: 10px; padding: 30px; max-width: 400px; text-align: center;">
                <h3 style="margin-bottom: 20px; color: #333;">📊 使用统计</h3>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                    <div style="padding: 15px; background: #e3f2fd; border-radius: 8px;">
                        <div style="font-size: 24px; font-weight: bold; color: #1976d2;">${stats.totalFills}</div>
                        <div style="color: #1976d2; font-size: 14px;">总填表次数</div>
                    </div>
                    <div style="padding: 15px; background: #f3e5f5; border-radius: 8px;">
                        <div style="font-size: 24px; font-weight: bold; color: #7b1fa2;">${stats.totalBackups}</div>
                        <div style="color: #7b1fa2; font-size: 14px;">备份数量</div>
                    </div>
                    <div style="padding: 15px; background: #e8f5e8; border-radius: 8px;">
                        <div style="font-size: 24px; font-weight: bold; color: #388e3c;">${stats.configSaves}</div>
                        <div style="color: #388e3c; font-size: 14px;">配置保存</div>
                    </div>
                    <div style="padding: 15px; background: #ffebee; border-radius: 8px;">
                        <div style="font-size: 24px; font-weight: bold; color: #d32f2f;">${stats.totalErrors}</div>
                        <div style="color: #d32f2f; font-size: 14px;">错误次数</div>
                    </div>
                </div>

                <div style="text-align: left; margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                    <div style="margin-bottom: 8px;"><strong>版本:</strong> ${stats.version}</div>
                    <div style="margin-bottom: 8px;"><strong>最后使用:</strong> ${stats.lastUsed}</div>
                    <div><strong>成功率:</strong> ${stats.totalFills > 0 ? Math.round(((stats.totalFills - stats.totalErrors) / stats.totalFills) * 100) : 100}%</div>
                </div>

                <div style="display: flex; gap: 10px;">
                    <button onclick="exportStats()" style="flex: 1; padding: 10px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        导出统计
                    </button>
                    <button onclick="this.parentElement.parentElement.parentElement.remove()"
                            style="flex: 1; padding: 10px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        关闭
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(statsDialog);

        // 导出统计数据
        window.exportStats = () => {
            const statsData = {
                ...stats,
                exportTime: new Date().toISOString(),
                records: JSON.parse(GM_getValue('fill-records', '[]')),
                performance: GM_getValue('last-performance', '{}')
            };

            const blob = new Blob([JSON.stringify(statsData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `copyright-assistant-stats-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);

            showStatus('统计数据已导出', 'success');
        };
    }

    // 延迟添加统计按钮
    setTimeout(addStatsToMenu, 2000);

    // 更新最后使用时间
    GM_setValue('last-used', new Date().toLocaleString('zh-CN'));

    // 添加主题切换功能
    function createThemeToggle() {
        const themeButton = document.createElement('button');
        themeButton.textContent = '🌙';
        themeButton.title = '切换主题';
        themeButton.style.cssText = `
            position: fixed;
            bottom: 200px;
            right: 20px;
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: #495057;
            color: white;
            font-size: 16px;
            cursor: pointer;
            z-index: 9999;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        `;

        themeButton.onclick = toggleTheme;
        document.body.appendChild(themeButton);

        // 应用保存的主题
        const savedTheme = GM_getValue('theme', 'light');
        if (savedTheme === 'dark') {
            applyDarkTheme();
            themeButton.textContent = '☀️';
        }
    }

    // 切换主题
    function toggleTheme() {
        const currentTheme = GM_getValue('theme', 'light');
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';

        GM_setValue('theme', newTheme);

        if (newTheme === 'dark') {
            applyDarkTheme();
            document.querySelector('button[title="切换主题"]').textContent = '☀️';
        } else {
            removeDarkTheme();
            document.querySelector('button[title="切换主题"]').textContent = '🌙';
        }

        showStatus(`已切换到${newTheme === 'dark' ? '深色' : '浅色'}主题`, 'success');
    }

    // 应用深色主题
    function applyDarkTheme() {
        const style = document.createElement('style');
        style.id = 'dark-theme-style';
        style.textContent = `
            #ai-config-panel, #template-panel, #settings-panel, #debug-panel {
                background: #2d3748 !important;
                color: white !important;
                border-color: #4a5568 !important;
            }

            #ai-config-panel input, #ai-config-panel select,
            #template-panel input, #template-panel select,
            #settings-panel input, #settings-panel select {
                background: #4a5568 !important;
                color: white !important;
                border-color: #718096 !important;
            }

            #ai-config-panel label,
            #template-panel label,
            #settings-panel label {
                color: #e2e8f0 !important;
            }

            #status {
                background: #4a5568 !important;
                color: white !important;
            }
        `;

        document.head.appendChild(style);
    }

    // 移除深色主题
    function removeDarkTheme() {
        const style = document.getElementById('dark-theme-style');
        if (style) {
            style.remove();
        }
    }

    // 添加主题切换按钮
    setTimeout(createThemeToggle, 1500);

    // 最终的清理和优化
    setTimeout(() => {
        // 清理内存
        cleanupMemory();

        // 记录启动完成
        debugLog('所有功能模块加载完成');

        // 显示完整功能列表
        if (GM_getValue('debug-mode', false)) {
            console.log('🎉 版权注册助手功能清单:');
            console.log('• AI智能填表 - 自动生成专业内容');
            console.log('• 表单验证 - 检查内容质量');
            console.log('• 内容优化 - AI改进现有内容');
            console.log('• 草稿管理 - 保存和恢复填写进度');
            console.log('• 模板系统 - 快速应用预设配置');
            console.log('• 配置管理 - 导入导出设置');
            console.log('• 性能监控 - 实时系统状态');
            console.log('• 调试模式 - 详细日志记录');
            console.log('• 快捷键 - 提高操作效率');
            console.log('• 主题切换 - 深色/浅色模式');
            console.log('• 使用统计 - 数据分析');
            console.log('• 自动备份 - 数据安全保障');
        }

    }, 3000);

})();

