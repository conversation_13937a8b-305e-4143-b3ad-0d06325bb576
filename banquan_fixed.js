// ==UserScript==
// @name         版权注册自动填表助手 (修复版)
// @namespace    http://tampermonkey.net/
// @version      1.1
// @description  使用AI自动填写版权注册表单 - 修复版
// <AUTHOR>
// @match        https://register.ccopyright.com.cn/r11.html*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_xmlhttpRequest
// ==/UserScript==

(function() {
    'use strict';

    // 全局状态
    let isInitialized = false;

    // 工具函数
    function log(message) {
        console.log(`[版权助手] ${message}`);
    }

    function showStatus(message, type = 'info') {
        const status = document.getElementById('status');
        if (status) {
            status.style.display = 'block';
            status.textContent = message;
            status.style.background = type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1';
            status.style.color = type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460';

            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }
    }

    // 等待元素出现
    function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            function check() {
                const element = document.querySelector(selector) ||
                               document.evaluate(selector, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;

                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`元素未找到: ${selector}`));
                } else {
                    setTimeout(check, 100);
                }
            }

            check();
        });
    }

    // 填写表单字段
    async function fillField(selector, value) {
        try {
            const element = await waitForElement(selector);

            // 清空现有内容
            element.value = '';
            element.focus();

            // 设置新值
            element.value = value;

            // 触发多种事件确保兼容性
            const events = ['input', 'change', 'blur', 'keyup', 'keydown'];
            events.forEach(eventType => {
                const event = new Event(eventType, { bubbles: true, cancelable: true });
                element.dispatchEvent(event);
            });

            // 模拟用户输入
            if (element.setSelectionRange) {
                element.setSelectionRange(value.length, value.length);
            }

            // 验证填写是否成功
            await new Promise(resolve => setTimeout(resolve, 300));
            if (element.value !== value) {
                log(`字段值验证失败，期望: ${value.substring(0, 20)}..., 实际: ${element.value.substring(0, 20)}...`);
                // 重试一次
                element.value = value;
                element.dispatchEvent(new Event('input', { bubbles: true }));
            }

            await new Promise(resolve => setTimeout(resolve, 500));
            return true;
        } catch (error) {
            log(`填写字段失败 ${selector}: ${error.message}`);
            return false;
        }
    }

    // 增强的填写函数，带重试机制
    async function fillFieldWithRetry(selector, value, fieldName, maxRetries = 3) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                log(`尝试填写 ${fieldName} (第${attempt}次)`);

                const element = await waitForElement(selector, 5000);

                // 滚动到元素位置
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await new Promise(resolve => setTimeout(resolve, 500));

                // 点击元素获得焦点
                element.click();
                element.focus();

                // 清空现有内容
                element.value = '';
                element.dispatchEvent(new Event('input', { bubbles: true }));

                // 模拟逐字输入
                for (let i = 0; i < value.length; i++) {
                    element.value = value.substring(0, i + 1);
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                    await new Promise(resolve => setTimeout(resolve, 10));
                }

                // 触发各种事件
                const events = ['input', 'change', 'blur', 'keyup', 'paste'];
                events.forEach(eventType => {
                    element.dispatchEvent(new Event(eventType, { bubbles: true }));
                });

                // 验证填写结果
                await new Promise(resolve => setTimeout(resolve, 500));
                if (element.value === value) {
                    log(`✅ ${fieldName} 填写成功`);
                    return true;
                } else {
                    log(`⚠️ ${fieldName} 值验证失败，期望: "${value}", 实际: "${element.value}"`);
                    if (attempt === maxRetries) {
                        // 最后一次尝试，强制设置值
                        element.value = value;
                        element.dispatchEvent(new Event('input', { bubbles: true }));
                        element.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                }

            } catch (error) {
                log(`❌ ${fieldName} 填写失败 (第${attempt}次): ${error.message}`);
                if (attempt === maxRetries) {
                    return false;
                }
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        return false;
    }

    // 点击按钮
    async function clickButton(selector) {
        try {
            const element = await waitForElement(selector);
            element.click();
            await new Promise(resolve => setTimeout(resolve, 1000));
            return true;
        } catch (error) {
            log(`点击按钮失败 ${selector}: ${error.message}`);
            return false;
        }
    }

    // AI提示词生成
    function generatePrompt(softwareName, framework) {
        return `你是一个专业的软件版权注册助手。请根据以下信息生成软件版权注册表单的各项内容：

软件名称：${softwareName}
开发框架：${framework}

请为以下字段生成合适的内容，要求专业、准确、符合版权注册规范，严格按照字数要求：

1. 开发的硬件环境（50字以内，包括标点符号）
2. 运行的硬件环境（50字以内，包括标点符号）
3. 开发该软件的操作系统（50字以内，包括标点符号）
4. 软件开发环境/开发工具（50字以内，包括标点符号）
5. 该软件的运行平台/操作系统（50字以内，包括标点符号）
6. 软件运行支撑环境/支持软件（50字以内，包括标点符号）
7. 开发目的（50字以内，包括标点符号）
8. 面向领域/行业（50字以内，包括标点符号）
9. 软件的主要功能（100-200字，包括标点符号）
10. 软件的技术特点（100字以内，包括标点符号）

请以JSON格式返回，格式如下：
{
  "devHardware": "开发硬件环境内容",
  "runHardware": "运行硬件环境内容",
  "devOS": "开发操作系统内容",
  "devTools": "开发工具内容",
  "runPlatform": "运行平台内容",
  "supportSoftware": "支撑环境内容",
  "purpose": "开发目的内容",
  "industry": "面向领域内容",
  "mainFunction": "主要功能内容",
  "techFeatures": "技术特点内容"
}`;
    }

    // 字数验证函数
    function validateLength(text, maxLength, minLength = 0) {
        const length = text.length;
        if (length > maxLength) {
            return { valid: false, message: `内容过长(${length}字)，最多${maxLength}字` };
        }
        if (length < minLength) {
            return { valid: false, message: `内容过短(${length}字)，至少${minLength}字` };
        }
        return { valid: true, message: `字数合适(${length}字)` };
    }

    // 调用AI接口
    async function callAI(prompt) {
        const apiUrl = GM_getValue('api-url');
        const apiKey = GM_getValue('api-key');
        const modelName = GM_getValue('model-name', 'gpt-3.5-turbo');

        if (!apiUrl || !apiKey) {
            throw new Error('请先配置API地址和密钥');
        }

        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: apiUrl,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                data: JSON.stringify({
                    model: modelName,
                    messages: [
                        {
                            role: 'system',
                            content: '你是一个专业的软件版权注册助手，请根据用户提供的信息生成准确、专业的版权注册表单内容。请严格按照字数要求生成内容，确保字数准确。'
                        },
                        {
                            role: 'user',
                            content: prompt
                        }
                    ],
                    temperature: 0.7,
                    max_tokens: 2000
                }),
                timeout: 30000,
                onload: function(response) {
                    try {
                        const data = JSON.parse(response.responseText);
                        if (data.choices && data.choices[0]) {
                            const content = data.choices[0].message.content;
                            const jsonMatch = content.match(/\{[\s\S]*\}/);
                            if (jsonMatch) {
                                const result = JSON.parse(jsonMatch[0]);

                                // 验证字数要求
                                const validations = [
                                    { field: 'devHardware', max: 50, name: '开发硬件环境' },
                                    { field: 'runHardware', max: 50, name: '运行硬件环境' },
                                    { field: 'devOS', max: 50, name: '开发操作系统' },
                                    { field: 'devTools', max: 50, name: '开发工具' },
                                    { field: 'runPlatform', max: 50, name: '运行平台' },
                                    { field: 'supportSoftware', max: 50, name: '支撑环境' },
                                    { field: 'purpose', max: 50, name: '开发目的' },
                                    { field: 'industry', max: 50, name: '面向领域' },
                                    { field: 'mainFunction', max: 200, min: 100, name: '主要功能' },
                                    { field: 'techFeatures', max: 100, name: '技术特点' }
                                ];

                                let hasError = false;
                                const errors = [];

                                validations.forEach(v => {
                                    if (result[v.field]) {
                                        const validation = validateLength(result[v.field], v.max, v.min || 0);
                                        if (!validation.valid) {
                                            hasError = true;
                                            errors.push(`${v.name}: ${validation.message}`);
                                        }
                                    }
                                });

                                if (hasError) {
                                    log('字数验证失败: ' + errors.join('; '));
                                    // 仍然返回结果，但记录警告
                                }

                                resolve(result);
                            } else {
                                reject(new Error('AI返回格式错误'));
                            }
                        } else {
                            reject(new Error('AI响应格式错误'));
                        }
                    } catch (error) {
                        reject(new Error('解析AI响应失败: ' + error.message));
                    }
                },
                onerror: function(error) {
                    reject(new Error('API调用失败: ' + error.message));
                },
                ontimeout: function() {
                    reject(new Error('请求超时，请检查网络连接'));
                }
            });
        });
    }

    // 自动填表主函数
    async function autoFill() {
        try {
            showStatus('开始自动填表...', 'info');

            const softwareName = document.getElementById('software-name').value;
            const framework = document.getElementById('dev-framework').value;

            if (!softwareName || !framework) {
                showStatus('请先填写软件名称和开发框架', 'error');
                return;
            }

            // 调用AI生成内容
            showStatus('正在调用AI生成内容...', 'info');
            const prompt = generatePrompt(softwareName, framework);
            const aiResult = await callAI(prompt);

            // 使用新的XPath选择器填写表单
            const fields = [
                {
                    name: "开发的硬件环境",
                    selector: "//div[@class='application']/div[1]//textarea[@class='large']",
                    value: aiResult.devHardware
                },
                {
                    name: "运行的硬件环境",
                    selector: "//div[@class='application']/div[2]//textarea[@class='large']",
                    value: aiResult.runHardware
                },
                {
                    name: "开发该软件的操作系统",
                    selector: "//div[@class='application']/div[3]//textarea[@class='large']",
                    value: aiResult.devOS
                },
                {
                    name: "软件开发环境/开发工具",
                    selector: "//div[@class='application']/div[4]//textarea[@class='large']",
                    value: aiResult.devTools
                },
                {
                    name: "该软件的运行平台/操作系统",
                    selector: "//div[@class='application']/div[5]//textarea[@class='large']",
                    value: aiResult.runPlatform
                },
                {
                    name: "软件运行支撑环境/支持软件",
                    selector: "//div[@class='application']/div[6]//textarea[@class='large']",
                    value: aiResult.supportSoftware
                },
                {
                    name: "开发目的",
                    selector: "//div[@class='application']/div[9]//textarea[@class='large']",
                    value: aiResult.purpose
                },
                {
                    name: "面向领域/行业",
                    selector: "//div[@class='application']/div[10]//textarea[@class='large']",
                    value: aiResult.industry
                },
                {
                    name: "软件的主要功能",
                    selector: "//div[@class='application']/div[11]//textarea[@class='large']",
                    value: aiResult.mainFunction
                },
                {
                    name: "软件的技术特点",
                    selector: "//div[@class='application']/div[@class='fillin_item g_clearfix']//div[2]/textarea[@class='large']",
                    value: aiResult.techFeatures
                }
            ];

            for (let i = 0; i < fields.length; i++) {
                const field = fields[i];
                showStatus(`填写 ${field.name} (${i + 1}/${fields.length})...`, 'info');

                const success = await fillFieldWithRetry(field.selector, field.value, field.name);
                if (success) {
                    log(`✅ 成功填写: ${field.name} - ${field.value.substring(0, 30)}...`);
                } else {
                    log(`❌ 填写失败: ${field.name}`);
                    showStatus(`填写失败: ${field.name}`, 'error');
                }

                // 添加延迟避免过快操作
                await new Promise(resolve => setTimeout(resolve, 800));
            }

            showStatus('自动填表完成！', 'success');
            log('所有字段填写完成');

        } catch (error) {
            log('自动填表失败: ' + error.message);
            showStatus('填表失败: ' + error.message, 'error');
        }
    }

    // 创建配置界面
    function createConfigPanel() {
        if (document.getElementById('ai-config-panel')) {
            return; // 已存在
        }

        const panel = document.createElement('div');
        panel.id = 'ai-config-panel';
        panel.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 350px;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 15px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-family: Arial, sans-serif;
        `;

        panel.innerHTML = `
            <div style="margin-bottom: 15px; font-weight: bold; color: #007bff; font-size: 16px;">
                🤖 AI自动填表配置
            </div>

            <div style="margin-bottom: 10px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">API地址:</label>
                <input type="text" id="api-url" placeholder="https://api.openai.com/v1/chat/completions"
                       style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>

            <div style="margin-bottom: 10px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">API密钥:</label>
                <input type="password" id="api-key" placeholder="sk-..."
                       style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">模型名称:</label>
                <input type="text" id="model-name" placeholder="gpt-3.5-turbo"
                       style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>

            <div style="margin-bottom: 10px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">软件名称:</label>
                <input type="text" id="software-name" placeholder="请输入软件名称"
                       style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">开发框架:</label>
                <input type="text" id="dev-framework" placeholder="如: Vue.js, React, Spring Boot等"
                       style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>

            <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                <button id="save-config" style="flex: 1; padding: 10px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    保存配置
                </button>
                <button id="preview-content" style="flex: 1; padding: 10px; background: #ffc107; color: black; border: none; border-radius: 4px; cursor: pointer;">
                    预览内容
                </button>
            </div>

            <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                <button id="start-auto-fill" style="flex: 1; padding: 10px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    开始填表
                </button>
            </div>

            <button id="toggle-panel" style="width: 100%; padding: 8px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
                最小化
            </button>

            <div id="status" style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px; font-size: 12px; display: none;"></div>
        `;

        document.body.appendChild(panel);

        // 加载保存的配置
        loadConfig();

        // 绑定事件
        document.getElementById('save-config').onclick = saveConfig;
        document.getElementById('preview-content').onclick = previewContent;
        document.getElementById('start-auto-fill').onclick = autoFill;
        document.getElementById('toggle-panel').onclick = togglePanel;
    }

    // 加载配置
    function loadConfig() {
        const elements = {
            'api-url': GM_getValue('api-url', ''),
            'api-key': GM_getValue('api-key', ''),
            'model-name': GM_getValue('model-name', 'gpt-3.5-turbo'),
            'software-name': GM_getValue('software-name', ''),
            'dev-framework': GM_getValue('dev-framework', '')
        };

        Object.keys(elements).forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.value = elements[id];
            }
        });
    }

    // 保存配置
    function saveConfig() {
        const elements = ['api-url', 'api-key', 'model-name', 'software-name', 'dev-framework'];

        elements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                GM_setValue(id, element.value);
            }
        });

        showStatus('配置已保存！', 'success');
    }



    // 预览内容功能
    async function previewContent() {
        try {
            const softwareName = document.getElementById('software-name').value;
            const framework = document.getElementById('dev-framework').value;

            if (!softwareName || !framework) {
                showStatus('请先填写软件名称和开发框架', 'error');
                return;
            }

            showStatus('正在生成预览内容...', 'info');

            const prompt = generatePrompt(softwareName, framework);
            const aiResult = await callAI(prompt);

            // 创建预览对话框
            const previewDialog = document.createElement('div');
            previewDialog.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 10001;
                display: flex;
                align-items: center;
                justify-content: center;
            `;

            const fields = [
                { name: "开发的硬件环境", value: aiResult.devHardware, limit: "50字以内" },
                { name: "运行的硬件环境", value: aiResult.runHardware, limit: "50字以内" },
                { name: "开发该软件的操作系统", value: aiResult.devOS, limit: "50字以内" },
                { name: "软件开发环境/开发工具", value: aiResult.devTools, limit: "50字以内" },
                { name: "该软件的运行平台/操作系统", value: aiResult.runPlatform, limit: "50字以内" },
                { name: "软件运行支撑环境/支持软件", value: aiResult.supportSoftware, limit: "50字以内" },
                { name: "开发目的", value: aiResult.purpose, limit: "50字以内" },
                { name: "面向领域/行业", value: aiResult.industry, limit: "50字以内" },
                { name: "软件的主要功能", value: aiResult.mainFunction, limit: "100-200字" },
                { name: "软件的技术特点", value: aiResult.techFeatures, limit: "100字以内" }
            ];

            let previewHtml = '';
            fields.forEach(field => {
                const length = field.value.length;
                const lengthColor = length > 50 && field.limit.includes('50字以内') ? 'red' :
                                  length < 100 && field.limit.includes('100-200字') ? 'orange' :
                                  length > 200 && field.limit.includes('100-200字') ? 'red' :
                                  length > 100 && field.limit.includes('100字以内') ? 'red' : 'green';

                previewHtml += `
                    <div style="margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        <div style="font-weight: bold; color: #333; margin-bottom: 5px;">
                            ${field.name} <span style="color: ${lengthColor};">(${length}字 - ${field.limit})</span>
                        </div>
                        <div style="color: #666; line-height: 1.4;">${field.value}</div>
                    </div>
                `;
            });

            previewDialog.innerHTML = `
                <div style="background: white; border-radius: 10px; padding: 20px; max-width: 800px; max-height: 80vh; overflow-y: auto; margin: 20px;">
                    <h3 style="margin-bottom: 20px; text-align: center; color: #007bff;">📋 内容预览</h3>
                    ${previewHtml}
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="this.parentElement.parentElement.parentElement.remove()"
                                style="padding: 10px 30px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                            关闭预览
                        </button>
                        <button onclick="this.parentElement.parentElement.parentElement.remove(); document.getElementById('start-auto-fill').click();"
                                style="padding: 10px 30px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            确认并填表
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(previewDialog);
            showStatus('预览内容已生成', 'success');

        } catch (error) {
            log('预览失败: ' + error.message);
            showStatus('预览失败: ' + error.message, 'error');
        }
    }

    // 切换面板显示
    function togglePanel() {
        const panel = document.getElementById('ai-config-panel');
        const toggleButton = document.getElementById('toggle-panel');
        const isMinimized = panel.style.height === '50px';

        if (isMinimized) {
            // 展开面板
            panel.style.height = 'auto';
            panel.style.overflow = 'visible';
            toggleButton.textContent = '最小化';
            toggleButton.style.background = '#6c757d';
        } else {
            // 最小化面板
            panel.style.height = '50px';
            panel.style.overflow = 'hidden';
            toggleButton.textContent = '🔼 展开';
            toggleButton.style.background = '#28a745';
            toggleButton.style.position = 'relative';
            toggleButton.style.zIndex = '10001';
        }
    }

    // 初始化
    function init() {
        if (isInitialized) return;

        try {
            log('版权注册助手启动');
            createConfigPanel();
            isInitialized = true;

            setTimeout(() => {
                showStatus('版权注册助手已就绪！', 'success');
            }, 1000);

        } catch (error) {
            log('初始化失败: ' + error.message);
        }
    }

    // 等待页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
